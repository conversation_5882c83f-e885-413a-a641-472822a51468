import threading
from database import Database

class UIOperationManager:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._initialize()
            return cls._instance
            
    def _initialize(self):
        self.db = Database()
        self.operation_lock = threading.Lock()
        self.pending_operations = {}
        
    def execute_ui_operation(self, operation_name, operation_func):
        with self.operation_lock:
            if operation_name in self.pending_operations:
                raise Exception(f"Operation {operation_name} already in progress")
            self.pending_operations[operation_name] = True
        try:
            @self.db.transaction(operation_name)
            def wrapped_operation(conn, cursor):
                return operation_func(conn, cursor)
            return wrapped_operation()
        finally:
            with self.operation_lock:
                self.pending_operations.pop(operation_name, None) 