import tkinter as tk
from tkinter import ttk
import time
from database import Database

class ActivityMonitor(tk.Toplevel):
    def __init__(self, parent):
        super().__init__(parent)
        self.title("Database Activity Monitor")
        self.geometry("1000x600")
        self.db = Database()
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(1, weight=1)
        self.header = ttk.Label(self, text="Active Database Transactions", font=('Helvetica', 14))
        self.header.grid(row=0, column=0, padx=10, pady=10, sticky='w')
        self.refresh_btn = ttk.Button(self, text="Refresh", command=self.update_data)
        self.refresh_btn.grid(row=0, column=1, padx=10, pady=10)
        self.tree = ttk.Treeview(self, columns=('Thread', 'Operation', 'Duration', 'Status'), show='headings')
        self.tree.heading('Thread', text='Thread ID')
        self.tree.heading('Operation', text='Operation')
        self.tree.heading('Duration', text='Duration (s)')
        self.tree.heading('Status', text='Status')
        self.tree.column('Thread', width=100)
        self.tree.column('Operation', width=400)
        self.tree.column('Duration', width=100)
        self.tree.column('Status', width=100)
        self.tree.grid(row=1, column=0, columnspan=2, padx=10, pady=10, sticky='nsew')
        scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.tree.yview)
        scrollbar.grid(row=1, column=2, sticky='ns')
        self.tree.configure(yscrollcommand=scrollbar.set)
        self.auto_refresh = tk.BooleanVar(value=True)
        self.auto_refresh_btn = ttk.Checkbutton(
            self, text="Auto-refresh (5s)", variable=self.auto_refresh,
            command=self.toggle_auto_refresh
        )
        self.auto_refresh_btn.grid(row=2, column=0, padx=10, pady=10, sticky='w')
        self.update_data()
        self.auto_refresh_job = None
        self.toggle_auto_refresh()

    def update_data(self):
        for item in self.tree.get_children():
            self.tree.delete(item)
        transactions = self.db.get_active_transactions()
        now = time.time()
        for thread_id, info in transactions.items():
            duration = info['duration'] if 'duration' in info else now - info.get('start_time', now)
            status = "OK" if duration < 5 else "WARNING" if duration < 30 else "CRITICAL"
            self.tree.insert('', 'end', values=(
                thread_id,
                info['operation'],
                f"{duration:.2f}",
                status
            ))
        for item in self.tree.get_children():
            values = self.tree.item(item)['values']
            if float(values[2]) > 5:
                self.tree.tag_configure('warning', background='#FFF3CD')
                self.tree.item(item, tags=('warning',))
            if float(values[2]) > 30:
                self.tree.tag_configure('critical', background='#F8D7DA')
                self.tree.item(item, tags=('critical',))

    def toggle_auto_refresh(self):
        if self.auto_refresh.get():
            self.auto_refresh_job = self.after(5000, self._auto_refresh)
        else:
            if self.auto_refresh_job:
                self.after_cancel(self.auto_refresh_job)
                self.auto_refresh_job = None

    def _auto_refresh(self):
        if self.auto_refresh.get():
            self.update_data()
            self.auto_refresh_job = self.after(5000, self._auto_refresh) 