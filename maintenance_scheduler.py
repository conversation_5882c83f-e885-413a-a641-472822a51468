import threading
import time
import logging
from datetime import time as dt_time, datetime, timedelta

class MaintenanceScheduler:
    def __init__(self, db):
        self.db = db
        self._thread = None
        self._stop_event = threading.Event()

    def start(self):
        if self._thread is not None:
            return
        self._stop_event.clear()
        self._thread = threading.Thread(
            target=self._run_scheduler,
            daemon=True
        )
        self._thread.start()

    def stop(self):
        if self._thread is None:
            return
        self._stop_event.set()
        self._thread.join()
        self._thread = None

    def _run_scheduler(self):
        while not self._stop_event.is_set():
            now = datetime.now()
            scheduled_time = dt_time(2, 0)
            if now.time() > scheduled_time:
                next_run = datetime.combine(
                    now.date() + timedelta(days=1),
                    scheduled_time
                )
            else:
                next_run = datetime.combine(now.date(), scheduled_time)
            wait_seconds = (next_run - now).total_seconds()
            if self._stop_event.wait(wait_seconds):
                break
            try:
                logging.info("Running scheduled database maintenance")
                self.db.perform_maintenance()
            except Exception as e:
                logging.error(f"Scheduled maintenance failed: {str(e)}") 