import sqlite3
import time
import logging
from typing import Optional

class DeadlockHandler:
    def __init__(self, db, max_retries=3, retry_delay=0.1):
        self.db = db
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
    def execute_with_retry(self, query, params=None, operation_name="Operation"):
        attempt = 0
        last_error = None
        while attempt < self.max_retries:
            attempt += 1
            success, result = self.db.execute_transaction(
                query,
                params,
                f"{operation_name} (attempt {attempt})"
            )
            if success:
                return (True, result)
            last_error = result
            time.sleep(self.retry_delay * attempt)
        logging.error(f"Failed after {self.max_retries} attempts: {last_error}")
        return (False, last_error)
    
    def batch_with_retry(self, operations, operation_name="Batch operation"):
        attempt = 0
        last_error = None
        while attempt < self.max_retries:
            attempt += 1
            success, result = self.db.execute_in_transaction(
                operations,
                f"{operation_name} (attempt {attempt})"
            )
            if success:
                return (True, result)
            last_error = result
            time.sleep(self.retry_delay * attempt)
        logging.error(f"Batch failed after {self.max_retries} attempts: {last_error}")
        return (False, last_error) 