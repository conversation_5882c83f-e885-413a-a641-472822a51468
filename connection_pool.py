import sqlite3
import threading
import queue
import os
from typing import Optional

class SQLiteConnectionPool:
    def __init__(self, db_path: str, max_connections: int = 5):
        self.db_path = db_path
        self.max_connections = max_connections
        self._pool = queue.Queue(maxsize=max_connections)
        self._lock = threading.Lock()
        self._connections_created = 0
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

    def _create_connection(self) -> sqlite3.Connection:
        conn = sqlite3.connect(self.db_path, timeout=30)
        conn.execute("PRAGMA journal_mode = WAL")
        conn.execute("PRAGMA synchronous = NORMAL")
        conn.execute("PRAGMA foreign_keys = ON")
        return conn

    def get_connection(self) -> sqlite3.Connection:
        try:
            conn = self._pool.get_nowait()
            try:
                conn.execute("SELECT 1")
                return conn
            except sqlite3.Error:
                self._connections_created -= 1
                return self._create_connection()
        except queue.Empty:
            with self._lock:
                if self._connections_created < self.max_connections:
                    self._connections_created += 1
                    return self._create_connection()
                else:
                    return self._pool.get()

    def return_connection(self, conn: sqlite3.Connection):
        try:
            conn.rollback()
            self._pool.put_nowait(conn)
        except queue.Full:
            conn.close()
            with self._lock:
                self._connections_created -= 1

    def close_all(self):
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                conn.close()
                with self._lock:
                    self._connections_created -= 1
            except queue.Empty:
                break 