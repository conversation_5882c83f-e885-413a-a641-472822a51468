#!/usr/bin/env python3
"""
Simple validation test for the customer import functionality.
Tests the validation rules without requiring the full GUI.
"""

import pandas as pd
import re

def validate_customer_data(row, row_index):
    """Validate customer data before import - standalone version"""
    errors = []
    
    try:
        # Basic field validation
        user_name = str(row['user_name']).strip() if pd.notna(row['user_name']) else ""
        name = str(row['name']).strip() if pd.notna(row['name']) else ""
        
        if not user_name:
            errors.append("User name is required")
        elif len(user_name) > 20:
            errors.append("User name must not exceed 20 characters")
        elif not re.match(r'^[a-zA-Z0-9.]+$', user_name):
            errors.append("User name must contain only letters, numbers, and dots")
            
        if not name:
            errors.append("Customer name is required")
        elif len(name) > 20:
            errors.append("Customer name must not exceed 20 characters")
            
        # Financial validation
        outstanding = float(row['outstanding']) if pd.notna(row['outstanding']) else 0.0
        credit = float(row['credit']) if pd.notna(row['credit']) else 0.0
        
        # Rule 1: No negative amounts
        if outstanding < 0:
            errors.append("Outstanding amount cannot be negative")
        if credit < 0:
            errors.append("Credit amount cannot be negative")
            
        # Rule 2: Credit and outstanding cannot both be positive
        if outstanding > 0 and credit > 0:
            errors.append("Customer cannot have both outstanding and credit amounts greater than zero")
            
        # Phone validation
        phone = str(row['phone']).strip() if pd.notna(row['phone']) else ""
        if phone and not re.match(r'^03\d{9}$', phone):
            errors.append("Phone number must be in format 03XXXXXXXXX")
            
        # Status validation
        status = str(row['status']).strip().lower() if pd.notna(row['status']) else ""
        if status not in ['active', 'inactive']:
            errors.append("Status must be 'Active' or 'Inactive'")
            
    except (ValueError, TypeError) as e:
        errors.append(f"Data type error: {str(e)}")
    except Exception as e:
        errors.append(f"Validation error: {str(e)}")
        
    return errors

def test_validation_rules():
    """Test the validation rules with various test cases"""
    print("Testing Customer Import Validation Rules")
    print("=" * 50)
    
    # Test data with various validation scenarios
    test_data = {
        'id': [1, 2, 3, 4, 5, 6, 7, 8],
        'user_name': ['user1', 'user2', 'invalid@user', '', 'toolongusernamethatexceeds20chars', 'user6', 'user7', 'user8'],
        'name': ['John Doe', 'Jane Smith', 'Test User', '', 'Very Long Name That Exceeds Twenty Characters', 'Valid User', 'Another User', 'Bob Wilson'],
        'phone': ['03001234567', '03009876543', '123456789', '03001234567', '03001111111', '03002222222', '', '03003333333'],
        'package': ['Basic', 'Premium', 'Basic', 'Basic', 'Basic', 'Basic', 'Basic', 'Basic'],
        'region': ['North', 'South', 'North', 'North', 'North', 'North', 'North', 'North'],
        'status': ['Active', 'Active', 'Active', 'Active', 'Active', 'Invalid', 'Active', 'Inactive'],
        'outstanding': [0.0, 500.0, 0.0, -100.0, 0.0, 1000.0, 0.0, 0.0],  # Row 4 has negative, Row 6 has both
        'credit': [1500.0, 0.0, 0.0, 0.0, 0.0, 500.0, 0.0, 0.0]  # Row 6 has both outstanding and credit
    }
    
    df = pd.DataFrame(test_data)
    
    print(f"Testing validation on {len(df)} rows...\n")
    
    validation_results = []
    for index, row in df.iterrows():
        errors = validate_customer_data(row, index)
        validation_results.append({
            'row': index + 1,
            'user_name': str(row.get('user_name', 'Unknown')),
            'errors': errors,
            'valid': len(errors) == 0
        })
    
    # Print validation results
    print("Validation Results:")
    print("-" * 60)
    
    valid_count = 0
    invalid_count = 0
    
    for result in validation_results:
        if result['valid']:
            print(f"✅ Row {result['row']} ({result['user_name']}): VALID")
            valid_count += 1
        else:
            print(f"❌ Row {result['row']} ({result['user_name']}): INVALID")
            for error in result['errors']:
                print(f"   • {error}")
            invalid_count += 1
        print()
    
    print(f"Validation Summary:")
    print(f"Valid rows: {valid_count}")
    print(f"Invalid rows: {invalid_count}")
    print(f"Total rows: {len(df)}")
    
    # Test specific validation rules
    print("\nTesting Specific Validation Rules:")
    print("-" * 40)
    
    # Test 1: Negative amounts
    test_row = {'user_name': 'test', 'name': 'Test User', 'phone': '03001234567', 
                'status': 'Active', 'outstanding': -100, 'credit': 0}
    errors = validate_customer_data(test_row, 0)
    negative_test = any("negative" in error.lower() for error in errors)
    print(f"✅ Negative amount validation: {'PASS' if negative_test else 'FAIL'}")
    
    # Test 2: Both outstanding and credit positive
    test_row = {'user_name': 'test', 'name': 'Test User', 'phone': '03001234567', 
                'status': 'Active', 'outstanding': 100, 'credit': 200}
    errors = validate_customer_data(test_row, 0)
    both_positive_test = any("both" in error.lower() for error in errors)
    print(f"✅ Both positive amounts validation: {'PASS' if both_positive_test else 'FAIL'}")
    
    # Test 3: Invalid phone format
    test_row = {'user_name': 'test', 'name': 'Test User', 'phone': '123456789', 
                'status': 'Active', 'outstanding': 0, 'credit': 0}
    errors = validate_customer_data(test_row, 0)
    phone_test = any("format" in error.lower() for error in errors)
    print(f"✅ Phone format validation: {'PASS' if phone_test else 'FAIL'}")
    
    # Test 4: Invalid user name characters
    test_row = {'user_name': 'test@user', 'name': 'Test User', 'phone': '03001234567', 
                'status': 'Active', 'outstanding': 0, 'credit': 0}
    errors = validate_customer_data(test_row, 0)
    username_test = any("letters" in error.lower() for error in errors)
    print(f"✅ User name character validation: {'PASS' if username_test else 'FAIL'}")
    
    # Test 5: Empty required fields
    test_row = {'user_name': '', 'name': '', 'phone': '03001234567', 
                'status': 'Active', 'outstanding': 0, 'credit': 0}
    errors = validate_customer_data(test_row, 0)
    required_test = any("required" in error.lower() for error in errors)
    print(f"✅ Required field validation: {'PASS' if required_test else 'FAIL'}")
    
    print(f"\n🎉 Validation testing completed!")
    print(f"The validation system correctly identified {invalid_count} invalid rows out of {len(df)} total rows.")
    
    return True

if __name__ == "__main__":
    test_validation_rules()
