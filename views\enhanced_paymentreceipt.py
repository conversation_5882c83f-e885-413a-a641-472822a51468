import tkinter as tk
from tkinter import ttk, messagebox
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

class LoginView(tk.Frame):
    COLORS = {
        'primary': '#3db3f2',
        'secondary': '#272953',
        'success': '#80b737',
        'danger': '#d04f51',
        'warning': '#e1e1e1',
        'light': '#272953',
        'background': '#16113a',
        'text_primary': '#e0e1e6',
        'text_secondary': '#e0e1e6',
        'border': '#272953',
        'div_background': '#272953'
    }

    def __init__(self, parent, login_callback):
        super().__init__(parent)
        self.login_callback = login_callback
        self.config(bg=self.COLORS['background'])
        
        # Set window to use full available space
        parent.state('zoomed')  # For Windows to maximize
        
        # Center container with fixed width
        container = tk.Frame(self, bg=self.COLORS['background'])
        container.place(relx=0.5, rely=0.5, anchor="center")

        # Login card with fixed width
        card = tk.Frame(
            container,
            bg=self.COLORS['div_background'],
            padx=30,
            pady=30,
            highlightbackground=self.COLORS['border'],
            highlightthickness=2,
            width=400  # Fixed width
        )
        card.pack()
        card.pack_propagate(False)  # Prevent shrinking to fit content

        # Title
        tk.Label(
            card,
            text="SANI BROADBAND - Login",
            font=("Segoe UI", 20, "bold"),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['div_background']
        ).pack(pady=(0, 20))

        # Username
        tk.Label(
            card,
            text="Username:",
            font=("Segoe UI", 12),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['div_background']
        ).pack(anchor="w")
        self.username_entry = tk.Entry(
            card,
            width=30,
            font=("Segoe UI", 11),
            bg=self.COLORS['light'],
            fg=self.COLORS['text_primary'],
            insertbackground=self.COLORS['text_primary']
        )
        self.username_entry.pack(pady=(0, 10))

        # Password
        tk.Label(
            card,
            text="Password:",
            font=("Segoe UI", 12),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['div_background']
        ).pack(anchor="w")
        self.password_entry = tk.Entry(
            card,
            width=30,
            font=("Segoe UI", 11),
            show="*",
            bg=self.COLORS['light'],
            fg=self.COLORS['text_primary'],
            insertbackground=self.COLORS['text_primary']
        )
        self.password_entry.pack(pady=(0, 20))

        # Login Button
        tk.Button(
            card,
            text="Login",
            command=self._attempt_login,
            bg=self.COLORS['success'],
            fg=self.COLORS['text_primary'],
            font=("Segoe UI", 12, "bold"),
            relief=tk.FLAT,
            padx=20,
            pady=10,
            activebackground=self.COLORS['success'],
            activeforeground=self.COLORS['text_primary']
        ).pack(pady=(0, 10))

        # Register Button
        tk.Button(
            card,
            text="Register",
            command=self._open_register_dialog,
            bg=self.COLORS['primary'],
            fg=self.COLORS['text_primary'],
            font=("Segoe UI", 12, "bold"),
            relief=tk.FLAT,
            padx=20,
            pady=10,
            activebackground=self.COLORS['primary'],
            activeforeground=self.COLORS['text_primary']
        ).pack()

        # Bind Enter key to login
        self.password_entry.bind("<Return>", lambda event: self._attempt_login())

        # Log current users for debugging
        logging.debug(f"Users in database at startup: {self.master.master.db.list_users()}")

    def _attempt_login(self):
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        logging.debug(f"Login attempt with username: {username}")

        if not username or not password:
            messagebox.showerror("Error", "Please enter both username and password")
            return

        success = self.login_callback(username, password)
        if success:
            logging.debug("Login successful, navigating to dashboard")
            messagebox.showinfo("Success", f"Welcome, {username}")
        else:
            logging.debug("Login failed")
            messagebox.showerror("Error", "Invalid username or password")

    def _open_register_dialog(self):
        dialog = tk.Toplevel(self)
        dialog.title("Register New User")
        dialog.transient(self)
        dialog.grab_set()
        dialog.configure(bg=self.COLORS['light'])
        dialog.geometry("400x300")
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        main_frame = tk.Frame(
            dialog,
            bg=self.COLORS['light'],
            highlightbackground=self.COLORS['border'],
            highlightthickness=2,
            padx=20,
            pady=20
        )
        main_frame.pack(fill="both", expand=True)

        tk.Label(
            main_frame,
            text="Username:",
            font=("Segoe UI", 12),
            bg=self.COLORS['light'],
            fg=self.COLORS['text_primary']
        ).grid(row=0, column=0, sticky="e", padx=(0, 10), pady=10)
        username_entry = tk.Entry(
            main_frame,
            width=30,
            font=("Segoe UI", 11)
        )
        username_entry.grid(row=0, column=1, padx=(0, 10), pady=10)

        tk.Label(
            main_frame,
            text="Password:",
            font=("Segoe UI", 12),
            bg=self.COLORS['light'],
            fg=self.COLORS['text_primary']
        ).grid(row=1, column=0, sticky="e", padx=(0, 10), pady=10)
        password_entry = tk.Entry(
            main_frame,
            width=30,
            font=("Segoe UI", 11),
            show="*"
        )
        password_entry.grid(row=1, column=1, padx=(0, 10), pady=10)

        tk.Label(
            main_frame,
            text="Confirm Password:",
            font=("Segoe UI", 12),
            bg=self.COLORS['light'],
            fg=self.COLORS['text_primary']
        ).grid(row=2, column=0, sticky="e", padx=(0, 10), pady=10)
        confirm_entry = tk.Entry(
            main_frame,
            width=30,
            font=("Segoe UI", 11),
            show="*"
        )
        confirm_entry.grid(row=2, column=1, padx=(0, 10), pady=10)

        def submit():
            username = username_entry.get().strip()
            password = password_entry.get().strip()
            confirm = confirm_entry.get().strip()
            logging.debug(f"Registration attempt for username: {username}")

            if not username or not password:
                messagebox.showerror("Error", "Username and password are required")
                return
            if password != confirm:
                messagebox.showerror("Error", "Passwords do not match")
                return

            success = self.master.master.db.register_user(username, password)
            if success:
                messagebox.showinfo("Success", f"User {username} registered successfully. Please log in.")
                dialog.destroy()
                self.username_entry.delete(0, tk.END)
                self.password_entry.delete(0, tk.END)
                self.username_entry.focus_set()
                logging.debug(f"Users after registration: {self.master.master.db.list_users()}")
            else:
                messagebox.showerror("Error", "Username already exists or registration failed")

        tk.Button(
            main_frame,
            text="Register",
            command=submit,
            bg=self.COLORS['success'],
            fg=self.COLORS['text_primary'],
            font=("Segoe UI", 12, "bold"),
            relief=tk.FLAT,
            padx=20,
            pady=10,
            activebackground=self.COLORS['success'],
            activeforeground=self.COLORS['text_primary']
        ).grid(row=3, columnspan=2, pady=20)
