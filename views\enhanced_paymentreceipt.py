import tkinter as tk
from tkinter import messagebox, simpledialog
import sqlite3
import os
from datetime import datetime
import time
from views.PaymentReceipt import PaymentReceipt  # Importing the original PaymentReceipt class

try:
    from reportlab.lib.pagesizes import A4
    from reportlab.pdfgen import canvas
    from reportlab.lib.units import mm
    import webbrowser
except ImportError as e:
    messagebox.showerror("Error", "Required module 'reportlab' is missing. Please install it using 'pip install reportlab'.")
    raise ImportError("Missing dependencies") from e

class EnhancedPaymentReceipt(PaymentReceipt):
    def _print_receipt(self, receipt_window):
        """Print the receipt content as PDF with invoice number as filename and share via WhatsApp"""
        try:
            # Get invoice number from the receipt window
            customer_frame = receipt_window.winfo_children()[0].winfo_children()[1]
            date_invoice_frame = customer_frame.winfo_children()[0]
            invoice_number = None
            for child in date_invoice_frame.winfo_children():
                text = child.cget("text")
                if "Invoice Number" in text:
                    invoice_number = text.split(": ")[1].strip()
                    break
            if not invoice_number or invoice_number == "NONE":
                invoice_number = f"INV_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            # Create PDF
            temp_dir = os.path.join(os.getenv('APPDATA'), 'CRM_System')
            os.makedirs(temp_dir, exist_ok=True)
            pdf_path = os.path.join(temp_dir, f"{invoice_number}.pdf")

            # Ensure unique filename to avoid overwriting
            base, ext = os.path.splitext(pdf_path)
            counter = 1
            while os.path.exists(pdf_path):
                pdf_path = f"{base}_{counter}{ext}"
                counter += 1

            c = canvas.Canvas(pdf_path, pagesize=A4)
            width, height = A4
            margin = 20 * mm
            line_height = 14
            y_position = height - margin

            # Helper function to draw text
            def draw_text(text, x, y, font="Helvetica", size=10, bold=False, align="left"):
                c.setFont(f"{font}-Bold" if bold else font, size)
                if align == "center":
                    c.drawCentredString(x, y, text)
                else:
                    c.drawString(x, y, text)
                return y - line_height

            # Helper function to draw table row
            def draw_table_row(data, x, y, col_widths, font="Helvetica", size=10, bold=False):
                x_pos = x
                for i, text in enumerate(data):
                    c.setFont(f"{font}-Bold" if bold else font, size)
                    c.drawString(x_pos, y, str(text))
                    x_pos += col_widths[i]
                return y - line_height

            # Title
            y_position = draw_text("SANI BROADBAND", width / 2, y_position, size=16, bold=True, align="center")
            y_position -= 20

            # Date and Invoice Number
            for child in date_invoice_frame.winfo_children():
                y_position = draw_text(child.cget("text"), margin, y_position, size=10)
            y_position -= 20

            # Customer Table
            y_position = draw_text("Customer Information", margin, y_position, size=12, bold=True)
            headers = ["Customer Name", "Customer Cell", "Bill ID", "Signature"]
            col_widths = [80 * mm, 50 * mm, 30 * mm, 30 * mm]
            customer_data = [customer_frame.grid_slaves(row=2, column=col)[0].cget("text") for col in range(4)]
            customer_data[3] = "-"  # Signature placeholder

            # Draw table border
            c.setLineWidth(0.5)
            c.rect(margin, y_position - line_height * 3 - 2, sum(col_widths), line_height * 3 + 4)

            # Draw headers
            y_position = draw_table_row(headers, margin, y_position, col_widths, size=10, bold=True)
            y_position = draw_text("-" * 80, margin, y_position, size=10)
            y_position = draw_table_row(customer_data, margin, y_position, col_widths, size=10)
            y_position -= 20

            # Package Table
            package_frame = receipt_window.winfo_children()[0].winfo_children()[2]
            y_position = draw_text("Package Details", margin, y_position, size=12, bold=True)
            headers = ["Qty.", "Package Plan", "Month", "Unit Price (PKR)", "Disc.", "Line Total (PKR)"]
            col_widths = [20 * mm, 60 * mm, 40 * mm, 30 * mm, 20 * mm, 30 * mm]
            package_data = [package_frame.grid_slaves(row=1, column=col)[0].cget("text") for col in range(6)]

            # Draw table border
            c.setLineWidth(0.5)
            c.rect(margin, y_position - line_height * 12 - 2, sum(col_widths), line_height * 12 + 4)

            # Draw headers
            y_position = draw_table_row(headers, margin, y_position, col_widths, size=10, bold=True)
            y_position = draw_text("-" * 80, margin, y_position, size=10)
            y_position = draw_table_row(package_data, margin, y_position, col_widths, size=10)

            # Empty rows
            empty_row = ["", "", "", "", "-", ""]
            for _ in range(8):
                y_position = draw_table_row(empty_row, margin, y_position, col_widths, size=10)

            # Amount Paid and Outstanding Bill
            amount_paid_label = package_frame.grid_slaves(row=10, column=4)[0].cget("text")
            amount_paid_value = package_frame.grid_slaves(row=10, column=5)[0].cget("text")
            outstanding_label = package_frame.grid_slaves(row=11, column=4)[0].cget("text")
            outstanding_value = package_frame.grid_slaves(row=11, column=5)[0].cget("text")

            # Draw totals
            y_position -= 10
            totals_col_widths = [140 * mm, 30 * mm, 30 * mm]
            y_position = draw_table_row(["", amount_paid_label, amount_paid_value], margin, y_position, totals_col_widths, size=10)
            y_position = draw_table_row(["", outstanding_label, outstanding_value], margin, y_position, totals_col_widths, size=10)
            y_position -= 20

            # Footer
            footer_label = receipt_window.winfo_children()[0].winfo_children()[3]
            for line in footer_label.cget("text").split("\n"):
                y_position = draw_text(line, width / 2, y_position, size=8, align="center")
                y_position -= 10

            # Thank You
            thank_you_label = receipt_window.winfo_children()[0].winfo_children()[4]
            y_position = draw_text(thank_you_label.cget("text"), width / 2, y_position, size=8, align="center")

            c.showPage()
            c.save()

            # Verify PDF was created
            if not os.path.exists(pdf_path):
                messagebox.showerror("Error", f"Failed to create PDF at: {pdf_path}")
                return

            # Open PDF
            os.startfile(pdf_path)

            # Ask for phone number to share via WhatsApp
            phone_number = simpledialog.askstring(
                "WhatsApp Share",
                "Enter phone number (with country code, e.g., +923001234567) to share receipt, or cancel to skip:"
            )
            if phone_number:
                # Clean and validate phone number
                phone_number = phone_number.replace(" ", "").replace("-", "")
                if not phone_number.startswith("+"):
                    phone_number = "+" + phone_number
                if not phone_number[1:].isdigit() or len(phone_number) < 10:
                    messagebox.showerror("Error", "Invalid phone number format.")
                    return

                # Open WhatsApp Web
                whatsapp_url = f"https://web.whatsapp.com/send?phone={phone_number}"
                webbrowser.open(whatsapp_url)

                # Brief delay to ensure WhatsApp Web opens
                time.sleep(2)

                # Inform user about manual attachment
                messagebox.showinfo(
                    "WhatsApp Share",
                    f"WhatsApp Web is opening. Please manually attach the PDF from:\n{pdf_path}\n"
                    "Note: Automatic attachment is not supported; you need to select the file in WhatsApp."
                )

        except Exception as e:
            messagebox.showerror("Error", f"Failed to process receipt: {str(e)}")
            raise

login.py:

import tkinter as tk
from tkinter import ttk, messagebox
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

class LoginView(tk.Frame):
    COLORS = {
        'primary': '#3db3f2',
        'secondary': '#272953',
        'success': '#80b737',
        'danger': '#d04f51',
        'warning': '#e1e1e1',
        'light': '#272953',
        'background': '#16113a',
        'text_primary': '#e0e1e6',
        'text_secondary': '#e0e1e6',
        'border': '#272953',
        'div_background': '#272953'
    }

    def __init__(self, parent, login_callback):
        super().__init__(parent)
        self.login_callback = login_callback
        self.config(bg=self.COLORS['background'])

        # Center container
        container = tk.Frame(self, bg=self.COLORS['background'])
        container.place(relx=0.5, rely=0.5, anchor="center")

        # Login card
        card = tk.Frame(
            container,
            bg=self.COLORS['div_background'],
            padx=30,
            pady=30,
            highlightbackground=self.COLORS['border'],
            highlightthickness=2
        )
        card.pack()

        # Title
        tk.Label(
            card,
            text="SANI BROADBAND - Login",
            font=("Segoe UI", 20, "bold"),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['div_background']
        ).pack(pady=(0, 20))

        # Username
        tk.Label(
            card,
            text="Username:",
            font=("Segoe UI", 12),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['div_background']
        ).pack(anchor="w")
        self.username_entry = tk.Entry(
            card,
            width=30,
            font=("Segoe UI", 11),
            bg=self.COLORS['light'],
            fg=self.COLORS['text_primary'],
            insertbackground=self.COLORS['text_primary']
        )
        self.username_entry.pack(pady=(0, 10))

        # Password
        tk.Label(
            card,
            text="Password:",
            font=("Segoe UI", 12),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['div_background']
        ).pack(anchor="w")
        self.password_entry = tk.Entry(
            card,
            width=30,
            font=("Segoe UI", 11),
            show="*",
            bg=self.COLORS['light'],
            fg=self.COLORS['text_primary'],
            insertbackground=self.COLORS['text_primary']
        )
        self.password_entry.pack(pady=(0, 20))

        # Login Button
        tk.Button(
            card,
            text="Login",
            command=self._attempt_login,
            bg=self.COLORS['success'],
            fg=self.COLORS['text_primary'],
            font=("Segoe UI", 12, "bold"),
            relief=tk.FLAT,
            padx=20,
            pady=10,
            activebackground=self.COLORS['success'],
            activeforeground=self.COLORS['text_primary']
        ).pack(pady=(0, 10))

        # Register Button
        tk.Button(
            card,
            text="Register",
            command=self._open_register_dialog,
            bg=self.COLORS['primary'],
            fg=self.COLORS['text_primary'],
            font=("Segoe UI", 12, "bold"),
            relief=tk.FLAT,
            padx=20,
            pady=10,
            activebackground=self.COLORS['primary'],
            activeforeground=self.COLORS['text_primary']
        ).pack()

        # Bind Enter key to login
        self.password_entry.bind("<Return>", lambda event: self._attempt_login())

        # Log current users for debugging
        logging.debug(f"Users in database at startup: {self.master.master.db.list_users()}")

    def _attempt_login(self):
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        logging.debug(f"Login attempt with username: {username}")

        if not username or not password:
            messagebox.showerror("Error", "Please enter both username and password")
            return

        success = self.login_callback(username, password)
        if success:
            logging.debug("Login successful, navigating to dashboard")
            messagebox.showinfo("Success", f"Welcome, {username}")
        else:
            logging.debug("Login failed")
            messagebox.showerror("Error", "Invalid username or password")

    def _open_register_dialog(self):
        dialog = tk.Toplevel(self)
        dialog.title("Register New User")
        dialog.transient(self)
        dialog.grab_set()
        dialog.configure(bg=self.COLORS['light'])
        dialog.geometry("400x300")
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f"{width}x{height}+{x}+{y}")

        main_frame = tk.Frame(
            dialog,
            bg=self.COLORS['light'],
            highlightbackground=self.COLORS['border'],
            highlightthickness=2,
            padx=20,
            pady=20
        )
        main_frame.pack(fill="both", expand=True)

        tk.Label(
            main_frame,
            text="Username:",
            font=("Segoe UI", 12),
            bg=self.COLORS['light'],
            fg=self.COLORS['text_primary']
        ).grid(row=0, column=0, sticky="e", padx=(0, 10), pady=10)
        username_entry = tk.Entry(
            main_frame,
            width=30,
            font=("Segoe UI", 11)
        )
        username_entry.grid(row=0, column=1, padx=(0, 10), pady=10)

        tk.Label(
            main_frame,
            text="Password:",
            font=("Segoe UI", 12),
            bg=self.COLORS['light'],
            fg=self.COLORS['text_primary']
        ).grid(row=1, column=0, sticky="e", padx=(0, 10), pady=10)
        password_entry = tk.Entry(
            main_frame,
            width=30,
            font=("Segoe UI", 11),
            show="*"
        )
        password_entry.grid(row=1, column=1, padx=(0, 10), pady=10)

        tk.Label(
            main_frame,
            text="Confirm Password:",
            font=("Segoe UI", 12),
            bg=self.COLORS['light'],
            fg=self.COLORS['text_primary']
        ).grid(row=2, column=0, sticky="e", padx=(0, 10), pady=10)
        confirm_entry = tk.Entry(
            main_frame,
            width=30,
            font=("Segoe UI", 11),
            show="*"
        )
        confirm_entry.grid(row=2, column=1, padx=(0, 10), pady=10)

        def submit():
            username = username_entry.get().strip()
            password = password_entry.get().strip()
            confirm = confirm_entry.get().strip()
            logging.debug(f"Registration attempt for username: {username}")

            if not username or not password:
                messagebox.showerror("Error", "Username and password are required")
                return
            if password != confirm:
                messagebox.showerror("Error", "Passwords do not match")
                return

            success = self.master.master.db.register_user(username, password)
            if success:
                messagebox.showinfo("Success", f"User {username} registered successfully. Please log in.")
                dialog.destroy()
                self.username_entry.delete(0, tk.END)
                self.password_entry.delete(0, tk.END)
                self.username_entry.focus_set()
                logging.debug(f"Users after registration: {self.master.master.db.list_users()}")
            else:
                messagebox.showerror("Error", "Username already exists or registration failed")

        tk.Button(
            main_frame,
            text="Register",
            command=submit,
            bg=self.COLORS['success'],
            fg=self.COLORS['text_primary'],
            font=("Segoe UI", 12, "bold"),
            relief=tk.FLAT,
            padx=20,
            pady=10,
            activebackground=self.COLORS['success'],
            activeforeground=self.COLORS['text_primary']
        ).grid(row=3, columnspan=2, pady=20)
