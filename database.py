import sqlite3
import os
from datetime import datetime, timedelta
import bcrypt
import csv
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from google_auth_oauthlib.flow import InstalledAppFlow
from google.oauth2.credentials import Credentials
import json
import pandas as pd
import shutil
import logging
import time
import zipfile
from google.auth.transport.requests import Request
import threading
from typing import Optional, Callable

# Set up logging to help diagnose issues
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

class Database:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._initialize()
            return cls._instance

    def _initialize(self):
        appdata_path = os.getenv('APPDATA')
        if not appdata_path:
            raise EnvironmentError("APPDATA environment variable not found. Cannot determine database path.")
        self.db_path = os.path.join(appdata_path, 'CRM_System', 'crm_database.db')
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self._setup_database()
        self.active_transactions = {}
        self.transaction_lock = threading.Lock()
        self.last_maintenance = None
        self._ensure_db_and_tables()
        self._migrate_database()
        self.perform_maintenance(force=False)

    def _ensure_database_directory(self):
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

    def get_connection(self):
        conn = sqlite3.connect(self.db_path, timeout=30)
        thread_id = threading.get_ident()
        return conn

    def return_connection(self, conn):
        thread_id = threading.get_ident()
        conn.close()

    def begin_exclusive_transaction(self):
        conn = self.get_connection()
        thread_id = threading.get_ident()
        try:
            conn.execute("BEGIN EXCLUSIVE TRANSACTION")
            return conn
        except Exception:
            conn.close()
            raise

    def perform_maintenance(self, force=False):
        try:
            if not force and self.last_maintenance and \
               (datetime.now() - self.last_maintenance) < timedelta(days=1):
                return
            with sqlite3.connect(self.db_path, timeout=30) as conn:
                logging.info("Starting database maintenance...")
                start_time = datetime.now()
                conn.execute("VACUUM")
                logging.info(f"VACUUM completed in {(datetime.now() - start_time).total_seconds():.2f}s")
                start_time = datetime.now()
                conn.execute("REINDEX")
                logging.info(f"REINDEX completed in {(datetime.now() - start_time).total_seconds():.2f}s")
                start_time = datetime.now()
                conn.execute("ANALYZE")
                logging.info(f"ANALYZE completed in {(datetime.now() - start_time).total_seconds():.2f}s")
                conn.execute("PRAGMA wal_checkpoint(TRUNCATE)")
                self.last_maintenance = datetime.now()
                logging.info("Database maintenance completed successfully")
        except Exception as e:
            logging.error(f"Database maintenance failed: {str(e)}")
            raise

    def close(self):
        self.pool.close_all()

    def _ensure_db_and_tables(self):
        """Ensure the database and its tables exist."""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self.conn = sqlite3.connect(self.db_path)
        self._create_tables()

    def _create_tables(self):
        if not self.conn:
            self.get_connection()
        
        # Create users table with additional fields
        c = self.conn.cursor()
        c.execute('''CREATE TABLE IF NOT EXISTS users (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     username TEXT NOT NULL UNIQUE,
                     email TEXT UNIQUE,
                     password TEXT NOT NULL,
                     is_locked INTEGER DEFAULT 0,
                     lock_until TEXT,
                     reset_attempts INTEGER DEFAULT 0,
                     last_reset_attempt TEXT
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_users_username ON users (username)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_users_email ON users (email)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_users_is_locked ON users (is_locked)''')

        # Email configuration table
        c.execute('''CREATE TABLE IF NOT EXISTS email_config (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     email TEXT NOT NULL,
                     password TEXT NOT NULL,
                     smtp_server TEXT NOT NULL,
                     smtp_port INTEGER NOT NULL
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_email_config_email ON email_config (email)''')

        # Password history table
        c.execute('''CREATE TABLE IF NOT EXISTS password_history (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     user_id INTEGER NOT NULL,
                     hashed_password TEXT NOT NULL,
                     created_at TEXT NOT NULL,
                     FOREIGN KEY (user_id) REFERENCES users(id)
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_password_history_user_id ON password_history (user_id)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_password_history_created_at ON password_history (created_at)''')

        # Password reset tokens table
        c.execute('''CREATE TABLE IF NOT EXISTS reset_tokens (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     user_id INTEGER NOT NULL,
                     token TEXT NOT NULL,
                     created_at TEXT NOT NULL,
                     attempts INTEGER DEFAULT 0,
                     FOREIGN KEY (user_id) REFERENCES users(id)
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_reset_tokens_user_id ON reset_tokens (user_id)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_reset_tokens_token ON reset_tokens (token)''')

        # Backup history table
        c.execute('''CREATE TABLE IF NOT EXISTS backup_history (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     timestamp TEXT NOT NULL,
                     type TEXT NOT NULL,
                     location TEXT NOT NULL,
                     status TEXT NOT NULL,
                     error_message TEXT
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_history_timestamp ON backup_history (timestamp)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_history_type ON backup_history (type)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_history_status ON backup_history (status)''')

        # Backup configuration table
        c.execute('''CREATE TABLE IF NOT EXISTS backup_config (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     smtp_email TEXT NOT NULL UNIQUE,
                     smtp_password TEXT NOT NULL,
                     smtp_server TEXT NOT NULL,
                     smtp_port INTEGER NOT NULL,
                     is_verified INTEGER DEFAULT 0
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_config_smtp_email ON backup_config (smtp_email)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_config_is_verified ON backup_config (is_verified)''')

        # Backup schedule table
        c.execute('''CREATE TABLE IF NOT EXISTS backup_schedule (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     type TEXT NOT NULL,
                     date TEXT NOT NULL,
                     time TEXT NOT NULL,
                     backup_path TEXT,
                     storage_location TEXT NOT NULL,
                     backup_email TEXT,
                     is_active INTEGER DEFAULT 1
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_schedule_is_active ON backup_schedule (is_active)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_schedule_type ON backup_schedule (type)''')

        # Google Drive configuration table
        c.execute('''CREATE TABLE IF NOT EXISTS google_drive_config (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     client_id TEXT NOT NULL,
                     client_secret TEXT NOT NULL,
                     token_path TEXT NOT NULL,
                     is_configured INTEGER DEFAULT 0
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_google_drive_config_is_configured ON google_drive_config (is_configured)''')

        # Create customers table with updated schema including credit_balance
        c.execute('''CREATE TABLE IF NOT EXISTS customers (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     name TEXT NOT NULL,
                     phone TEXT,
                     address TEXT,
                     region TEXT,
                     package_id INTEGER,
                     status INTEGER DEFAULT 1,
                     credit_balance REAL DEFAULT 0,
                     outstanding_amount REAL DEFAULT 0,
                     create_date TEXT,
                     package_change_date TEXT,
                     user_name TEXT NOT NULL UNIQUE,
                     FOREIGN KEY (package_id) REFERENCES packages(id)
                     )''')
        
        # Check if outstanding_amount column exists, if not add it
        c.execute("PRAGMA table_info(customers)")
        columns = [col[1] for col in c.fetchall()]
        if 'outstanding_amount' not in columns:
            c.execute("ALTER TABLE customers ADD COLUMN outstanding_amount REAL DEFAULT 0")
        
        # Create indexes after ensuring columns exist
        c.execute('''CREATE INDEX IF NOT EXISTS idx_customers_name ON customers (name)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers (phone)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_customers_region ON customers (region)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_customers_status ON customers (status)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_customers_credit_balance ON customers (credit_balance)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_customers_outstanding_amount ON customers (outstanding_amount)''')

        c.execute('''CREATE TABLE IF NOT EXISTS packages (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     name TEXT NOT NULL UNIQUE,
                     price REAL NOT NULL,
                     description TEXT
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_packages_name ON packages (name)''')

        c.execute('''CREATE TABLE IF NOT EXISTS products (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     name TEXT NOT NULL UNIQUE,
                     price REAL NOT NULL,
                     description TEXT
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_products_name ON products (name)''')

        c.execute('''CREATE TABLE IF NOT EXISTS regions (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     name TEXT NOT NULL UNIQUE
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_regions_name ON regions (name)''')

        # Updated billing table with all required columns
        c.execute('''CREATE TABLE IF NOT EXISTS billing (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     customer_id INTEGER,
                     month INTEGER NOT NULL,
                     year INTEGER NOT NULL,
                     amount REAL NOT NULL,
                     paid_amount REAL DEFAULT 0,
                     is_paid INTEGER DEFAULT 0,
                     paid_date TEXT,
                     invoice_number TEXT,
                     paid_by TEXT,
                     status TEXT DEFAULT 'Unpaid',
                     is_manual INTEGER DEFAULT 0,
                     outstanding_amount REAL DEFAULT 0,
                     credit_amount REAL DEFAULT 0,
                     FOREIGN KEY (customer_id) REFERENCES customers(id)
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_billing_customer_id ON billing (customer_id)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_billing_is_paid ON billing (is_paid)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_billing_paid_date ON billing (paid_date)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_billing_outstanding ON billing (outstanding_amount)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_billing_credit ON billing (credit_amount)''')

        # Create billing audit log table
        c.execute('''CREATE TABLE IF NOT EXISTS billing_audit_log (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     action TEXT NOT NULL,
                     bill_id INTEGER NOT NULL,
                     user TEXT NOT NULL,
                     changes TEXT NOT NULL,
                     timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                     FOREIGN KEY (bill_id) REFERENCES billing(id)
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_billing_audit_bill_id ON billing_audit_log (bill_id)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_billing_audit_timestamp ON billing_audit_log (timestamp)''')

        c.execute('''CREATE TABLE IF NOT EXISTS customer_purchases (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     customer_id INTEGER NOT NULL,
                     product_id INTEGER NOT NULL,
                     billing_id INTEGER,
                     purchase_date TEXT,
                     FOREIGN KEY (customer_id) REFERENCES customers(id),
                     FOREIGN KEY (product_id) REFERENCES products(id),
                     FOREIGN KEY (billing_id) REFERENCES billing(id)
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_customer_purchases_customer_id ON customer_purchases (customer_id)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_customer_purchases_product_id ON customer_purchases (product_id)''')

        # Payment history table to track all payment records
        c.execute('''CREATE TABLE IF NOT EXISTS payment_history (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     invoice_number TEXT NOT NULL,
                     customer_id INTEGER NOT NULL,
                     payment_date TEXT NOT NULL,
                     amount_paid REAL NOT NULL,
                     credit_amount REAL DEFAULT 0,
                     outstanding_amount REAL DEFAULT 0,
                     paid_by TEXT,
                     payment_method TEXT,
                     notes TEXT,
                     timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                     FOREIGN KEY (customer_id) REFERENCES customers(id)
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_payment_history_invoice ON payment_history (invoice_number)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_payment_history_customer ON payment_history (customer_id)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_payment_history_date ON payment_history (payment_date)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_payment_history_timestamp ON payment_history (timestamp)''')

        c.execute('''CREATE TABLE IF NOT EXISTS stock (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     product_id INTEGER UNIQUE NOT NULL,
                     quantity INTEGER NOT NULL DEFAULT 0,
                     sold INTEGER NOT NULL DEFAULT 0,
                     FOREIGN KEY (product_id) REFERENCES products(id),
                     CONSTRAINT product_unique UNIQUE (product_id)
                     )''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_stock_product_id ON stock (product_id)''')

        # Insert default data
        self._insert_default_data(self.conn)

        # New tables
        c.execute('''CREATE TABLE IF NOT EXISTS sessions (
                     id INTEGER PRIMARY KEY AUTOINCREMENT,
                     user_id INTEGER,
                     token TEXT NOT NULL,
                     created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                     expires_at DATETIME,
                     FOREIGN KEY (user_id) REFERENCES users(id)
                     )''')

        # Add columns if they don't exist
        self._add_column_if_not_exists(c, 'billing', 'is_manual', 'INTEGER DEFAULT 0')
        self._add_column_if_not_exists(c, 'billing', 'outstanding_amount', 'REAL DEFAULT 0')
        self._add_column_if_not_exists(c, 'billing', 'credit_amount', 'REAL DEFAULT 0')
        self._add_column_if_not_exists(c, 'customers', 'credit_balance', 'REAL DEFAULT 0.0')
        self._add_column_if_not_exists(c, 'customers', 'outstanding_amount', 'REAL DEFAULT 0.0')

        self.conn.commit()
        # self.conn.close() # This was closing the connection prematurely

    def _insert_default_data(self, conn):
        """Insert default data into the database"""
        c = conn.cursor()
        
        try:
            # Default packages
            default_packages = [
                ('Basic', 'Basic Package - 5 Mbps', 1500.0),
                ('Standard', 'Standard Package - 10 Mbps', 2500.0),
                ('Premium', 'Premium Package - 20 Mbps', 3500.0)
            ]
            c.executemany('''INSERT OR IGNORE INTO packages (name, description, price) 
                             VALUES (?, ?, ?)''', default_packages)

            # Default products
            default_products = [
                ('Router', 'Wireless Router', 5000.0),
                ('Modem', 'Cable Modem', 3000.0),
                ('Cable', 'Ethernet Cable (10m)', 500.0)
            ]
            c.executemany('''INSERT OR IGNORE INTO products (name, description, price) 
                             VALUES (?, ?, ?)''', default_products)

            # Default stock
            c.execute('''INSERT OR IGNORE INTO stock (product_id, quantity, sold)
                         SELECT p.id, 
                                CASE WHEN p.name = 'Router' THEN 100 
                                     WHEN p.name = 'Modem' THEN 100 
                                     WHEN p.name = 'Cable' THEN 200 
                                     ELSE 0 END,
                                0
                         FROM products p
                         WHERE NOT EXISTS (SELECT 1 FROM stock s WHERE s.product_id = p.id)''')

            # Default regions
            default_regions = [
                ('North',),
                ('South',),
                ('East',),
                ('West',)
            ]
            c.executemany('''INSERT OR IGNORE INTO regions (name) 
                             VALUES (?)''', default_regions)
            
            # Default admin user if no users exist
            c.execute("SELECT COUNT(*) FROM users")
            if c.fetchone()[0] == 0:
                hashed_password = bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt())
                c.execute("INSERT INTO users (username, email, password) VALUES (?, ?, ?)",
                         ("admin", "<EMAIL>", hashed_password.decode('utf-8')))
            
            conn.commit()
        except Exception as e:
            conn.rollback()
            logging.error(f"Error inserting default data: {str(e)}")
            raise

        conn.commit()
        # conn.close() # This was closing the connection prematurely

    def _add_column_if_not_exists(self, c, table_name, column_name, column_type):
        """Add a column to a table if it doesn't exist."""
        c.execute(f"PRAGMA table_info({table_name})")
        columns = [col[1] for col in c.fetchall()]
        if column_name not in columns:
            logging.info(f"Adding column '{column_name}' to table '{table_name}'")
            c.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}")

    def validate_billing_schema(self):
        """Validate billing table has all required columns"""
        required_columns = {
            'id', 'customer_id', 'month', 'year', 'amount', 
            'paid_amount', 'is_paid', 'paid_date', 'invoice_number',
            'paid_by', 'status', 'is_manual', 'outstanding_amount', 
            'credit_amount'
        }
        
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("PRAGMA table_info(billing)")
        existing_columns = {col[1] for col in c.fetchall()}
        missing_columns = required_columns - existing_columns
        
        if missing_columns:
            logging.warning(f"Missing columns in billing table: {missing_columns}")
            return False
        return True

    def validate_bill_data(self, bill_data):
        """Validate bill data before insertion/update"""
        if not all(k in bill_data for k in ['customer_id', 'month', 'year', 'amount']):
            raise ValueError("Missing required bill fields")
        
        if bill_data['month'] not in range(1, 13):
            raise ValueError("Month must be between 1-12")
        
        if bill_data['year'] < 2000 or bill_data['year'] > 2100:
            raise ValueError("Invalid year")
        
        if bill_data['amount'] <= 0:
            raise ValueError("Amount must be positive")

    def calculate_bill_status(self, amount, paid_amount):
        """Determine bill status based on payment"""
        if paid_amount >= amount:
            return 'Paid'
        elif paid_amount > 0:
            return 'Partially Paid'
        return 'Unpaid'

    def log_billing_change(self, action, bill_id, user, changes):
        """Log changes to billing records"""
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("""
            INSERT INTO billing_audit_log 
            (action, bill_id, user, changes, timestamp)
            VALUES (?, ?, ?, ?, datetime('now'))
        """, (action, bill_id, user, json.dumps(changes)))
        conn.commit()

    def generate_invoice_number(self):
        """Generate sequential invoice numbers"""
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT MAX(CAST(SUBSTR(invoice_number, 5) AS INTEGER)) FROM billing")
        last_num = c.fetchone()[0] or 0
        return f"INV-{str(last_num + 1).zfill(5)}"

    def get_customer_billing_summary(self, customer_id):
        """Get billing summary for a customer"""
        conn = self.get_connection()
        c = conn.cursor()
        
        c.execute("""
            SELECT 
                COUNT(*) as total_bills,
                SUM(amount) as total_amount,
                SUM(paid_amount) as total_paid,
                SUM(CASE WHEN is_paid = 0 THEN amount ELSE 0 END) as total_unpaid
            FROM billing 
            WHERE customer_id = ?
        """, (customer_id,))
        
        return dict(zip(
            ['total_bills', 'total_amount', 'total_paid', 'total_unpaid'],
            c.fetchone()
        ))

    def update_customer_outstanding_amount(self, customer_id, outstanding_amount):
        """Update customer's outstanding amount"""
        conn = self.get_connection()
        c = conn.cursor()
        try:
            c.execute("UPDATE customers SET outstanding_amount = ? WHERE id = ?", 
                     (outstanding_amount, customer_id))
            conn.commit()
        finally:
            conn.close()

    def update_customer_credit_amount(self, customer_id, credit_amount):
        """Update customer's credit balance"""
        conn = self.get_connection()
        c = conn.cursor()
        try:
            c.execute("UPDATE customers SET credit_balance = ? WHERE id = ?", 
                     (credit_amount, customer_id))
            conn.commit()
        finally:
            conn.close()

    def get_customer_outstanding_amount(self, customer_id):
        """Get customer's total outstanding amount from billing table"""
        conn = self.get_connection()
        c = conn.cursor()
        try:
            c.execute("""
                SELECT COALESCE(SUM(amount - paid_amount), 0) as total_outstanding
                FROM billing 
                WHERE customer_id = ? AND (amount - paid_amount) > 0
            """, (customer_id,))
            return c.fetchone()[0] or 0.0
        finally:
            conn.close()

    def get_customer_credit_amount(self, customer_id):
        """Get customer's current credit amount from payment history"""
        conn = self.get_connection()
        c = conn.cursor()
        try:
            c.execute("""
                SELECT credit_amount 
                FROM payment_history 
                WHERE customer_id = ? 
                ORDER BY timestamp DESC 
                LIMIT 1
            """, (customer_id,))
            result = c.fetchone()
            return result[0] if result else 0.0
        finally:
            conn.close()

    def sync_customer_financial_data(self, customer_id, preserve_imported_values=False):
        """Sync customer's outstanding and credit amounts from billing and payment history

        Args:
            customer_id: The customer ID to sync
            preserve_imported_values: If True, only update if calculated values are higher
                                    This preserves imported values while allowing normal operations
        """
        outstanding = self.get_customer_outstanding_amount(customer_id)
        credit = self.get_customer_credit_amount(customer_id)

        if preserve_imported_values:
            # Get current stored values
            conn = self.get_connection()
            c = conn.cursor()
            try:
                c.execute("SELECT outstanding_amount, credit_balance FROM customers WHERE id = ?", (customer_id,))
                result = c.fetchone()
                if result:
                    stored_outstanding, stored_credit = result
                    stored_outstanding = stored_outstanding or 0.0
                    stored_credit = stored_credit or 0.0

                    # Only update if calculated values are higher (indicating new activity)
                    if outstanding > stored_outstanding:
                        self.update_customer_outstanding_amount(customer_id, outstanding)
                    else:
                        outstanding = stored_outstanding

                    if credit > stored_credit:
                        self.update_customer_credit_amount(customer_id, credit)
                    else:
                        credit = stored_credit
            finally:
                conn.close()
        else:
            # Normal sync behavior - update with calculated values
            self.update_customer_outstanding_amount(customer_id, outstanding)
            self.update_customer_credit_amount(customer_id, credit)

        return outstanding, credit

    def validate_payment(self, bill_id, payment_amount):
        """Validate payment amount against bill"""
        conn = self.get_connection()
        c = conn.cursor()
        
        c.execute("""
            SELECT amount, paid_amount 
            FROM billing 
            WHERE id = ?
        """, (bill_id,))
        
        amount, paid_amount = c.fetchone()
        remaining = amount - (paid_amount or 0)
        
        if payment_amount <= 0:
            raise ValueError("Payment amount must be positive")
        if payment_amount > remaining * 1.5:  # Allow up to 50% overpayment
            raise ValueError("Payment amount exceeds reasonable overpayment")
        
        return remaining

    def generate_monthly_report(self, month, year):
        """Generate monthly billing report"""
        conn = self.get_connection()
        c = conn.cursor()
        
        c.execute("""
            SELECT 
                c.name as customer_name,
                b.amount,
                b.paid_amount,
                b.status,
                b.paid_date
            FROM billing b
            JOIN customers c ON b.customer_id = c.id
            WHERE b.month = ? AND b.year = ?
            ORDER BY b.status, c.name
        """, (month, year))
        
        return {
            'month': month,
            'year': year,
            'bills': [dict(zip(
                ['customer_name', 'amount', 'paid_amount', 'status', 'paid_date'],
                row
            )) for row in c.fetchall()]
        }

    def archive_paid_bills(self, cutoff_date):
        """Archive paid bills older than cutoff date"""
        conn = self.get_connection()
        c = conn.cursor()
        
        # Create archive table if not exists
        c.execute("""
            CREATE TABLE IF NOT EXISTS billing_archive AS 
            SELECT * FROM billing WHERE 1=0
        """)
        
        # Move old paid bills to archive
        c.execute("""
            INSERT INTO billing_archive 
            SELECT * FROM billing 
            WHERE is_paid = 1 AND paid_date < ?
        """, (cutoff_date,))
        
        # Delete archived bills
        c.execute("""
            DELETE FROM billing 
            WHERE is_paid = 1 AND paid_date < ?
        """, (cutoff_date,))
        
        conn.commit()
        return c.rowcount

    def get_bill_template(self, bill_type='standard'):
        """Get bill template configuration"""
        templates = {
            'standard': {
                'header': "Monthly Service Charge",
                'footer': "Thank you for your business",
                'fields': ['customer_name', 'month', 'year', 'amount', 'due_date']
            },
            'manual': {
                'header': "Manual Bill",
                'footer': "Please pay by the due date",
                'fields': ['customer_name', 'description', 'amount', 'due_date']
            }
        }
        return templates.get(bill_type, templates['standard'])

    # ... [Rest of the existing Database class methods remain unchanged] ...

    def _migrate_database(self):
        logging.info("Starting database migration")
        conn = sqlite3.connect(self.db_path)
        c = conn.cursor()

        # Migration for customers table
        try:
            c.execute("PRAGMA table_info(customers)")
            columns = [col[1] for col in c.fetchall()]
            
            # Add outstanding_amount column if it doesn't exist
            if 'outstanding_amount' not in columns:
                logging.info("Adding outstanding_amount column to customers table")
                c.execute("ALTER TABLE customers ADD COLUMN outstanding_amount REAL DEFAULT 0.0")
                
            # Add credit_balance column if it doesn't exist
            if 'credit_balance' not in columns:
                logging.info("Adding credit_balance column to customers table")
                c.execute("ALTER TABLE customers ADD COLUMN credit_balance REAL DEFAULT 0.0")
                
            conn.commit()
            logging.info("Customers table migration completed")
        except Exception as e:
            logging.error(f"Error during customers table migration: {str(e)}")
            conn.rollback()

        # Migration for billing table
        try:
            c.execute("PRAGMA table_info(billing)")
            columns = [col[1] for col in c.fetchall()]
            
            # Add status column if it doesn't exist
            if 'status' not in columns:
                logging.info("Adding status column to billing table")
                c.execute("ALTER TABLE billing ADD COLUMN status TEXT DEFAULT 'Unpaid'")
                
            # Add is_manual column if it doesn't exist
            if 'is_manual' not in columns:
                logging.info("Adding is_manual column to billing table")
                c.execute("ALTER TABLE billing ADD COLUMN is_manual INTEGER DEFAULT 0")
                
            # Add outstanding_amount column if it doesn't exist
            if 'outstanding_amount' not in columns:
                logging.info("Adding outstanding_amount column to billing table")
                c.execute("ALTER TABLE billing ADD COLUMN outstanding_amount REAL DEFAULT 0")
                
            # Add credit_amount column if it doesn't exist
            if 'credit_amount' not in columns:
                logging.info("Adding credit_amount column to billing table")
                c.execute("ALTER TABLE billing ADD COLUMN credit_amount REAL DEFAULT 0")
                
            # Create billing_audit_log table if not exists
            c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='billing_audit_log'")
            if not c.fetchone():
                logging.info("Creating billing_audit_log table")
                c.execute('''CREATE TABLE billing_audit_log (
                             id INTEGER PRIMARY KEY AUTOINCREMENT,
                             action TEXT NOT NULL,
                             bill_id INTEGER NOT NULL,
                             user TEXT NOT NULL,
                             changes TEXT NOT NULL,
                             timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                             FOREIGN KEY (bill_id) REFERENCES billing(id)
                             )''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_billing_audit_bill_id ON billing_audit_log (bill_id)''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_billing_audit_timestamp ON billing_audit_log (timestamp)''')
                
            conn.commit()
            logging.info("Billing table migration completed")
        except Exception as e:
            logging.error(f"Error during billing table migration: {str(e)}")
            conn.rollback()

        # ... [Rest of the existing _migrate_database method remains unchanged] ...

# ... [Rest of the file remains unchanged] ...

        # Migration for payment_history table
        try:
            c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='payment_history'")
            if not c.fetchone():
                c.execute('''CREATE TABLE payment_history (
                             id INTEGER PRIMARY KEY AUTOINCREMENT,
                             invoice_number TEXT NOT NULL,
                             customer_id INTEGER NOT NULL,
                             payment_date TEXT NOT NULL,
                             amount_paid REAL NOT NULL,
                             credit_amount REAL DEFAULT 0,
                             outstanding_amount REAL DEFAULT 0,
                             paid_by TEXT,
                             payment_method TEXT,
                             notes TEXT,
                             timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                             FOREIGN KEY (customer_id) REFERENCES customers(id)
                             )''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_payment_history_invoice ON payment_history (invoice_number)''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_payment_history_customer ON payment_history (customer_id)''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_payment_history_date ON payment_history (payment_date)''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_payment_history_timestamp ON payment_history (timestamp)''')
                
                # Migrate existing payment data from billing table
                c.execute('''
                    INSERT INTO payment_history (
                        invoice_number, customer_id, payment_date, amount_paid,
                        credit_amount, outstanding_amount, paid_by
                    )
                    SELECT 
                        invoice_number, customer_id, paid_date, paid_amount,
                        credit_amount, outstanding_amount, paid_by
                    FROM billing
                    WHERE is_paid = 1 AND paid_date IS NOT NULL
                ''')
                
                conn.commit()
                logging.info("Payment history table migration completed")
            else:
                # Ensure timestamp column exists
                c.execute("PRAGMA table_info(payment_history)")
                columns = [col[1] for col in c.fetchall()]
                if 'timestamp' not in columns:
                    c.execute("ALTER TABLE payment_history ADD COLUMN timestamp DATETIME DEFAULT CURRENT_TIMESTAMP")
                    c.execute('''CREATE INDEX IF NOT EXISTS idx_payment_history_timestamp ON payment_history (timestamp)''')
                    conn.commit()
        except Exception as e:
            logging.error(f"Error during payment history table migration: {str(e)}")
            conn.rollback()

        # Migration for backup_config table
        try:
            c.execute("PRAGMA table_info(backup_config)")
            columns = [col[1] for col in c.fetchall()]
            
            if not columns:  # Table doesn't exist
                c.execute('''CREATE TABLE backup_config (
                             id INTEGER PRIMARY KEY AUTOINCREMENT,
                             smtp_email TEXT NOT NULL UNIQUE,
                             smtp_password TEXT NOT NULL,
                             smtp_server TEXT NOT NULL,
                             smtp_port INTEGER NOT NULL,
                             is_verified INTEGER DEFAULT 0
                             )''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_config_smtp_email ON backup_config (smtp_email)''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_config_is_verified ON backup_config (is_verified)''')
            else:
                # Drop old table and create new one to simplify migration
                c.execute("DROP TABLE IF EXISTS backup_config")
                c.execute('''CREATE TABLE backup_config (
                             id INTEGER PRIMARY KEY AUTOINCREMENT,
                             smtp_email TEXT NOT NULL UNIQUE,
                             smtp_password TEXT NOT NULL,
                             smtp_server TEXT NOT NULL,
                             smtp_port INTEGER NOT NULL,
                             is_verified INTEGER DEFAULT 0
                             )''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_config_smtp_email ON backup_config (smtp_email)''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_config_is_verified ON backup_config (is_verified)''')
            
            conn.commit()
            logging.info("Backup_config table migration completed")
        except Exception as e:
            logging.error(f"Error during backup_config table migration: {str(e)}")
            conn.rollback()

        # Migration for backup_history table
        try:
            c.execute("PRAGMA table_info(backup_history)")
            columns = [col[1] for col in c.fetchall()]
            
            if 'timestamp' not in columns:
                c.execute("ALTER TABLE backup_history RENAME TO backup_history_old")
                c.execute('''CREATE TABLE backup_history (
                             id INTEGER PRIMARY KEY AUTOINCREMENT,
                             timestamp TEXT NOT NULL,
                             type TEXT NOT NULL,
                             location TEXT NOT NULL,
                             status TEXT NOT NULL,
                             error_message TEXT
                             )''')
                c.execute('''INSERT INTO backup_history (timestamp, type, location, status, error_message)
                             SELECT date, type, location, status, error_message
                             FROM backup_history_old''')
                c.execute("DROP TABLE backup_history_old")
            
            if 'status' not in columns:
                c.execute("ALTER TABLE backup_history ADD COLUMN status TEXT NOT NULL DEFAULT 'success'")
            if 'error_message' not in columns:
                c.execute("ALTER TABLE backup_history ADD COLUMN error_message TEXT")
            
            # Add indexes for backup_history
            c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_history_timestamp ON backup_history (timestamp)''')
            c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_history_type ON backup_history (type)''')
            c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_history_status ON backup_history (status)''')
            
            conn.commit()
            logging.info("Backup_history table migration completed")
        except Exception as e:
            logging.error(f"Error during backup_history table migration: {str(e)}")
            conn.rollback()

        # Migration for backup_schedule table
        try:
            c.execute("PRAGMA table_info(backup_schedule)")
            columns = [col[1] for col in c.fetchall()]
            
            if 'is_active' not in columns:
                c.execute("ALTER TABLE backup_schedule ADD COLUMN is_active INTEGER DEFAULT 1")
            
            # Add indexes for backup_schedule
            c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_schedule_is_active ON backup_schedule (is_active)''')
            c.execute('''CREATE INDEX IF NOT EXISTS idx_backup_schedule_type ON backup_schedule (type)''')
            
            conn.commit()
            logging.info("Backup_schedule table migration completed")
        except Exception as e:
            logging.error(f"Error during backup_schedule table migration: {str(e)}")
            conn.rollback()

        # Migration for stock table
        try:
            c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stock'")
            if c.fetchone():
                c.execute("PRAGMA index_list('stock')")
                indexes = c.fetchall()
                has_unique = any('product_unique' in index[1] for index in indexes)
                
                if not has_unique:
                    c.execute('''CREATE TABLE stock_new (
                                 id INTEGER PRIMARY KEY AUTOINCREMENT,
                                 product_id INTEGER UNIQUE,
                                 quantity INTEGER NOT NULL DEFAULT 0,
                                 sold INTEGER NOT NULL DEFAULT 0,
                                 FOREIGN KEY (product_id) REFERENCES products(id),
                                 CONSTRAINT product_unique UNIQUE (product_id)
                                 )''')
                    
                    c.execute('''INSERT INTO stock_new (product_id, quantity, sold)
                                 SELECT product_id, SUM(quantity), SUM(sold)
                                 FROM stock
                                 GROUP BY product_id''')
                    
                    c.execute("DROP TABLE stock")
                    c.execute("ALTER TABLE stock_new RENAME TO stock")
                    
                    # Add index for stock
                    c.execute('''CREATE INDEX IF NOT EXISTS idx_stock_product_id ON stock (product_id)''')
                    
                    conn.commit()
            logging.info("Stock table migration completed")
        except Exception as e:
            logging.error(f"Error during stock table migration: {str(e)}")
            conn.rollback()

        # Migration for password_history table
        try:
            c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='password_history'")
            if not c.fetchone():
                c.execute('''CREATE TABLE password_history (
                             id INTEGER PRIMARY KEY AUTOINCREMENT,
                             user_id INTEGER NOT NULL,
                             hashed_password TEXT NOT NULL,
                             created_at TEXT NOT NULL,
                             FOREIGN KEY (user_id) REFERENCES users(id)
                             )''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_password_history_user_id ON password_history (user_id)''')
                c.execute('''CREATE INDEX IF NOT EXISTS idx_password_history_created_at ON password_history (created_at)''')
                conn.commit()
            logging.info("Password_history table migration completed")
        except Exception as e:
            logging.error(f"Error during password_history table migration: {str(e)}")
            conn.rollback()

        # Migration for packages table
        try:
            c.execute('''CREATE INDEX IF NOT EXISTS idx_packages_name ON packages (name)''')
            conn.commit()
            logging.info("Packages table migration completed")
        except Exception as e:
            logging.error(f"Error during packages table migration: {str(e)}")
            conn.rollback()

        # Migration for products table
        try:
            c.execute('''CREATE INDEX IF NOT EXISTS idx_products_name ON products (name)''')
            conn.commit()
            logging.info("Products table migration completed")
        except Exception as e:
            logging.error(f"Error during products table migration: {str(e)}")
            conn.rollback()

        # Migration for regions table
        try:
            c.execute('''CREATE INDEX IF NOT EXISTS idx_regions_name ON regions (name)''')
            conn.commit()
            logging.info("Regions table migration completed")
        except Exception as e:
            logging.error(f"Error during regions table migration: {str(e)}")
            conn.rollback()

        # Migration for customer_purchases table
        try:
            c.execute('''CREATE INDEX IF NOT EXISTS idx_customer_purchases_customer_id ON customer_purchases (customer_id)''')
            c.execute('''CREATE INDEX IF NOT EXISTS idx_customer_purchases_product_id ON customer_purchases (product_id)''')
            c.execute('''CREATE INDEX IF NOT EXISTS idx_customer_purchases_billing_id ON customer_purchases (billing_id)''')
            c.execute('''CREATE INDEX IF NOT EXISTS idx_customer_purchases_purchase_date ON customer_purchases (purchase_date)''')
            conn.commit()
            logging.info("Customer_purchases table migration completed")
        except Exception as e:
            logging.error(f"Error during customer_purchases table migration: {str(e)}")
            conn.rollback()

        conn.close()
        logging.info("Database migration completed")

    def get_connection(self):
        """Get a new database connection if none exists"""
        if self.conn is None:
            logging.debug(f"Creating new database connection to {self.db_path}")
            self.conn = sqlite3.connect(self.db_path, timeout=30)
            self.conn.execute("PRAGMA journal_mode=WAL")
            self.conn.execute("PRAGMA synchronous=NORMAL")
            self.conn.execute("PRAGMA busy_timeout=5000")  # Set busy timeout to 5 seconds
        return self.conn

    def ensure_connection(self):
        """Ensure the database connection is open"""
        return self.get_connection()

    def close_connection(self):
        """Close the database connection if it exists"""
        if self.conn is not None:
            try:
                logging.debug("Committing and closing database connection")
                self.conn.commit()  # Commit any pending transactions
                self.conn.close()
            except Exception as e:
                logging.error(f"Error closing connection: {str(e)}")
            finally:
                self.conn = None

    def execute_with_retry(self, method, *args, retries=3, delay=1):
        """Execute a database method with retries on lock errors"""
        for attempt in range(retries):
            try:
                return method(*args)
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < retries - 1:
                    logging.warning(f"Database locked: {str(e)}, retrying in {delay} seconds...")
                    time.sleep(delay)
                    continue
                raise

    # Email configuration methods
    def get_email_config(self):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT * FROM email_config LIMIT 1")
        result = c.fetchone()
        conn.close()
        return result

    def save_email_config(self, email, password, smtp_server, smtp_port):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("DELETE FROM email_config")
        c.execute("INSERT INTO email_config (email, password, smtp_server, smtp_port) VALUES (?, ?, ?, ?)",
                 (email, password, smtp_server, smtp_port))
        conn.commit()
        conn.close()

    # Password history methods
    def save_password_history(self, user_id, hashed_password):
        conn = self.get_connection()
        c = conn.cursor()
        created_at = datetime.now().isoformat()
        c.execute("INSERT INTO password_history (user_id, hashed_password, created_at) VALUES (?, ?, ?)",
                 (user_id, hashed_password, created_at))
        # Keep only the last 5 passwords to avoid excessive storage
        c.execute("""
            DELETE FROM password_history
            WHERE user_id = ? AND id NOT IN (
                SELECT id FROM password_history WHERE user_id = ? 
                ORDER BY created_at DESC LIMIT 5
            )""", (user_id, user_id))
        conn.commit()
        conn.close()

    def get_password_history(self, user_id):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT hashed_password FROM password_history WHERE user_id = ?", (user_id,))
        result = c.fetchall()
        conn.close()
        return result

    # Backup configuration methods
    def get_backup_config(self):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT id, smtp_email, smtp_password, smtp_server, smtp_port, is_verified FROM backup_config LIMIT 1")
        result = c.fetchone()
        conn.close()
        return result

    def get_all_backup_configs(self):
        """Retrieve all backup configurations from the backup_config table"""
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT id, smtp_email, smtp_password, smtp_server, smtp_port, is_verified FROM backup_config")
        result = c.fetchall()
        conn.close()
        logging.debug(f"Retrieved all backup configs: {result}")
        return result

    def save_backup_config(self, smtp_email, smtp_password, smtp_server, smtp_port):
        """Save email configuration to the database."""
        try:
            conn = self.get_connection()
            c = conn.cursor()
            
            # Check if config exists with this email
            c.execute("SELECT id FROM backup_config WHERE smtp_email = ?", (smtp_email,))
            existing = c.fetchone()
            
            if existing:
                # Update existing config
                c.execute("""
                    UPDATE backup_config 
                    SET smtp_password = ?, smtp_server = ?, smtp_port = ?, is_verified = ?
                    WHERE id = ?
                """, (smtp_password, smtp_server, smtp_port, 0, existing[0]))
            else:
                # Insert new config
                c.execute("""
                    INSERT INTO backup_config 
                    (smtp_email, smtp_password, smtp_server, smtp_port, is_verified)
                    VALUES (?, ?, ?, ?, 0)
                """, (smtp_email, smtp_password, smtp_server, smtp_port))
            
            conn.commit()
            return True
        except sqlite3.IntegrityError as ie:
            if "UNIQUE constraint failed" in str(ie):
                raise ValueError("This email is already configured in the system")
            raise
        except Exception as e:
            logging.error(f"Error saving backup config: {str(e)}")
            raise

    def set_email_verified(self, smtp_email):
        """Mark an email configuration as verified."""
        try:
            conn = self.get_connection()
            c = conn.cursor()
            c.execute("""
                UPDATE backup_config 
                SET is_verified = 1 
                WHERE smtp_email = ?
            """, (smtp_email,))
            conn.commit()
            return c.rowcount > 0
        except Exception as e:
            logging.error(f"Error setting email verified: {str(e)}")
            raise

    def verify_backup_email(self, email):
        """Verify if an email exists and is verified in the database."""
        try:
            conn = self.get_connection()
            c = conn.cursor()
            c.execute("""
                SELECT smtp_email, smtp_password 
                FROM backup_config 
                WHERE smtp_email = ? AND is_verified = 1
            """, (email,))
            result = c.fetchone()
            return result  # Returns (email, password) if verified, None otherwise
        except Exception as e:
            logging.error(f"Error verifying backup email: {str(e)}")
            raise

    # Google Drive configuration methods
    def get_google_drive_config(self):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT * FROM google_drive_config LIMIT 1")
        result = c.fetchone()
        conn.close()
        return result

    def save_google_drive_config(self, client_id, client_secret, token_path):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("DELETE FROM google_drive_config")
        c.execute("INSERT INTO google_drive_config (client_id, client_secret, token_path, is_configured) VALUES (?, ?, ?, ?)",
                 (client_id, client_secret, token_path, 1))
        conn.commit()
        conn.close()

    def update_google_drive_config_status(self, is_configured):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("UPDATE google_drive_config SET is_configured = ?", (is_configured,))
        conn.commit()
        conn.close()

    def run_google_drive_config_wizard(self):
        wizard = GoogleDriveConfigWizard()
        return wizard.run()

    # Backup history methods
    def record_backup_history(self, backup_type, location, status="success", error_message=""):
        """Record backup operation in history"""
        def do_record():
            conn = self.get_connection()
            c = conn.cursor()
            c.execute("INSERT INTO backup_history (timestamp, type, location, status, error_message) VALUES (?, ?, ?, ?, ?)",
                     (datetime.now().isoformat(), backup_type, location, status, error_message))
            conn.commit()
            return True

        try:
            return self.execute_with_retry(do_record)
        except Exception as e:
            logging.error(f"Failed to record backup history: {str(e)}")
            return False
        finally:
            conn.close()

    def get_backup_history(self):
        """Retrieve backup history from the backup_history table"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            # Verify table schema
            cursor.execute("PRAGMA table_info(backup_history)")
            columns = [col[1] for col in cursor.fetchall()]
            expected_columns = ['id', 'timestamp', 'type', 'location', 'status', 'error_message']
            if not all(col in columns for col in expected_columns):
                logging.error(f"Backup history table schema mismatch. Expected: {expected_columns}, Found: {columns}")
                return []

            cursor.execute("""
                SELECT timestamp, type, location, status, error_message
                FROM backup_history
                ORDER BY timestamp DESC
            """)
            logs = cursor.fetchall()
            # Ensure each row has exactly 5 columns
            return [(log[0], log[1], log[2], log[3], log[4] if len(log) > 4 else '') for log in logs]
        except sqlite3.OperationalError as e:
            logging.error(f"Database error retrieving backup history: {str(e)}")
            return []
        except IndexError as e:
            logging.error(f"Index error in backup history data: {str(e)}")
            return []
        except Exception as e:
            logging.error(f"Unexpected error retrieving backup history: {str(e)}")
            return []
        finally:
            conn.close()

    # Backup schedule methods
    def get_scheduled_backups(self):
        """Retrieve all scheduled backups from the backup_schedule table"""
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT id, type, date, time, backup_path, storage_location, backup_email FROM backup_schedule WHERE is_active = 1")
        result = c.fetchall()
        conn.close()
        logging.debug(f"Retrieved scheduled backups: {result}")
        return result

    def save_scheduled_backup(self, job_id, backup_type, date_str, time_str, backup_path, storage_location, backup_email=None):
        """Save a scheduled backup to the backup_schedule table"""
        def do_save():
            conn = self.get_connection()
            c = conn.cursor()
            c.execute("INSERT INTO backup_schedule (id, type, date, time, backup_path, storage_location, backup_email) VALUES (?, ?, ?, ?, ?, ?, ?)",
                     (job_id, backup_type, date_str, time_str, backup_path, storage_location, backup_email))
            conn.commit()
            return True

        try:
            return self.execute_with_retry(do_save)
        except Exception as e:
            logging.error(f"Failed to save scheduled backup: {str(e)}")
            raise

    def get_scheduled_backup(self, job_id):
        """Retrieve a specific scheduled backup by job_id"""
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT id, type, date, time, backup_path, storage_location, backup_email FROM backup_schedule WHERE id = ?", (job_id,))
        result = c.fetchone()
        conn.close()
        logging.debug(f"Retrieved scheduled backup with ID {job_id}: {result}")
        return result

    def remove_scheduled_backup(self, job_id):
        """Remove a scheduled backup from the backup_schedule table"""
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("UPDATE backup_schedule SET is_active = 0 WHERE id = ?", (job_id,))
        conn.commit()
        conn.close()
        logging.info(f"Removed scheduled backup with ID {job_id}")

    # Password reset methods
    def create_reset_token(self, user_id, token):
        conn = self.get_connection()
        c = conn.cursor()
        now = datetime.now().isoformat()
        c.execute("DELETE FROM reset_tokens WHERE user_id = ?", (user_id,))
        c.execute("INSERT INTO reset_tokens (user_id, token, created_at) VALUES (?, ?, ?)",
                 (user_id, token, now))
        conn.commit()
        conn.close()

    def get_reset_token(self, user_id):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT id, token, created_at, attempts FROM reset_tokens WHERE user_id = ?", (user_id,))
        result = c.fetchone()
        conn.close()
        return result

    def increment_token_attempts(self, token_id):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("UPDATE reset_tokens SET attempts = attempts + 1 WHERE id = ?", (token_id,))
        conn.commit()
        conn.close()

    def delete_token(self, token_id):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("DELETE FROM reset_tokens WHERE id = ?", (token_id,))
        conn.commit()
        conn.close()

    # User account lock methods
    def lock_user_account(self, username, lock_minutes=60):
        conn = self.get_connection()
        c = conn.cursor()
        lock_until = (datetime.now() + timedelta(minutes=lock_minutes)).isoformat()
        c.execute("UPDATE users SET is_locked = 1, lock_until = ? WHERE username = ?", 
                 (lock_until, username))
        conn.commit()
        conn.close()

    def unlock_user(self, username):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("UPDATE users SET is_locked = 0, lock_until = NULL WHERE username = ?", (username,))
        conn.commit()
        conn.close()

    def is_user_locked(self, username):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT is_locked, lock_until FROM users WHERE username = ?", (username,))
        result = c.fetchone()
        conn.close()

        if not result:
            return False

        is_locked, lock_until = result
        if not is_locked:
            return False

        if lock_until and datetime.now() < datetime.fromisoformat(lock_until):
            return True
        else:
            self.unlock_user(username)
            return False

    # User reset attempts tracking
    def update_reset_attempts(self, username):
        conn = self.get_connection()
        c = conn.cursor()
        now = datetime.now().isoformat()
        c.execute("UPDATE users SET reset_attempts = reset_attempts + 1, last_reset_attempt = ? WHERE username = ?",
                 (now, username))
        conn.commit()
        conn.close()

    def get_reset_attempts(self, username):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT reset_attempts, last_reset_attempt FROM users WHERE username = ?", (username,))
        result = c.fetchone()
        conn.close()
        return result

    def reset_attempts_counter(self, username):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("UPDATE users SET reset_attempts = 0, last_reset_attempt = NULL WHERE username = ?", (username,))
        conn.commit()
        conn.close()

    # User management methods
    def add_user(self, username, email, password):
        conn = self.get_connection()
        c = conn.cursor()
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
        try:
            c.execute("INSERT INTO users (username, email, password) VALUES (?, ?, ?)",
                     (username, email, hashed_password.decode('utf-8')))
            user_id = c.lastrowid
            self.save_password_history(user_id, hashed_password.decode('utf-8'))
            conn.commit()
        except sqlite3.IntegrityError:
            raise ValueError("Username or email already exists")
        finally:
            conn.close()

    def verify_user(self, username, password):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT id, password, is_locked, lock_until FROM users WHERE username = ?", (username,))
        user = c.fetchone()
        conn.close()

        if not user:
            return False, None

        user_id, hashed_password, is_locked, lock_until = user

        if is_locked:
            if lock_until:
                lock_time = datetime.fromisoformat(lock_until)
                if datetime.now() < lock_time:
                    remaining = (lock_time - datetime.now()).total_seconds() / 60
                    raise ValueError(f"Account is locked. Try again in {int(remaining)} minutes")
                else:
                    c.execute("UPDATE users SET is_locked = 0, lock_until = NULL, reset_attempts = 0 WHERE id = ?", (user_id,))
                    conn.commit()

        if bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8')):
            return True, user_id
        return False, None

    def get_user_by_username(self, username):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT id, username, email FROM users WHERE username = ?", (username,))
        result = c.fetchone()
        conn.close()
        return result

    def update_user_password(self, user_id, new_password):
        conn = self.get_connection()
        c = conn.cursor()
        hashed_password = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt())
        c.execute("UPDATE users SET password = ? WHERE id = ?", 
                 (hashed_password.decode('utf-8'), user_id))
        self.save_password_history(user_id, hashed_password.decode('utf-8'))
        conn.commit()
        conn.close()

    # Customer management methods
    def add_customer(self, user_name, name, phone, package_id, region, credit_balance=0):
        conn = self.get_connection()
        c = conn.cursor()
        create_date = datetime.now().strftime('%Y-%m-%d')
        try:
            c.execute("""
                INSERT INTO customers (user_name, name, phone, package_id, create_date, status, region, credit_balance)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (user_name, name, phone, package_id, create_date, 1, region, credit_balance))
            conn.commit()
        except sqlite3.IntegrityError:
            raise ValueError("Username already exists")
        finally:
            conn.close()

    def update_customer(self, customer_id, user_name, name, phone, package_id, status, region, credit_balance=None):
        conn = self.get_connection()
        c = conn.cursor()
        try:
            if credit_balance is not None:
                c.execute("""
                    UPDATE customers 
                    SET user_name = ?, name = ?, phone = ?, package_id = ?, status = ?, region = ?, credit_balance = ?
                    WHERE id = ?
                """, (user_name, name, phone, package_id, status, region, credit_balance, customer_id))
            else:
                c.execute("""
                    UPDATE customers 
                    SET user_name = ?, name = ?, phone = ?, package_id = ?, status = ?, region = ?
                    WHERE id = ?
                """, (user_name, name, phone, package_id, status, region, customer_id))
            conn.commit()
        except sqlite3.IntegrityError:
            raise ValueError("Username already exists")
        finally:
            conn.close()

    def get_customer(self, customer_id):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT * FROM customers WHERE id = ?", (customer_id,))
        result = c.fetchone()
        conn.close()
        return result

    def get_all_customers(self):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT * FROM customers")
        result = c.fetchall()
        conn.close()
        return result

    # Package management methods
    def add_package(self, name, description, price):
        conn = self.get_connection()
        c = conn.cursor()
        try:
            c.execute("INSERT INTO packages (name, description, price) VALUES (?, ?, ?)",
                     (name, description, price))
            conn.commit()
        except sqlite3.IntegrityError:
            raise ValueError("Package name already exists")
        finally:
            conn.close()

    def get_all_packages(self):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT * FROM packages")
        result = c.fetchall()
        conn.close()
        return result

    # Product management methods
    def add_product(self, name, description, price):
        conn = self.get_connection()
        c = conn.cursor()
        try:
            c.execute("INSERT INTO products (name, description, price) VALUES (?, ?, ?)",
                     (name, description, price))
            product_id = c.lastrowid
            c.execute("INSERT OR IGNORE INTO stock (product_id, quantity, sold) VALUES (?, ?, ?)",
                     (product_id, 0, 0))
            conn.commit()
        except sqlite3.IntegrityError:
            raise ValueError("Product name already exists")
        finally:
            conn.close()

    def get_all_products(self):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT * FROM products")
        result = c.fetchall()
        conn.close()
        return result

    # Region management methods
    def add_region(self, name):
        conn = self.get_connection()
        c = conn.cursor()
        try:
            c.execute("INSERT INTO regions (name) VALUES (?)", (name,))
            conn.commit()
        except sqlite3.IntegrityError:
            raise ValueError("Region name already exists")
        finally:
            conn.close()

    def get_all_regions(self):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT * FROM regions")
        result = c.fetchall()
        conn.close()
        return result

    # Billing management methods
    def add_bill(self, customer_id, month, year, amount, paid_amount=0, is_paid=0):
        conn = self.get_connection()
        c = conn.cursor()
        try:
            c.execute("""
                INSERT INTO billing (customer_id, month, year, amount, paid_amount, is_paid)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (customer_id, month, year, amount, paid_amount, is_paid))
            conn.commit()
        except sqlite3.IntegrityError:
            raise ValueError("Bill already exists for this customer and period")
        finally:
            conn.close()

    def update_bill(self, bill_id, paid_amount, is_paid, paid_date, invoice_number, paid_by):
        conn = self.get_connection()
        c = conn.cursor()
        try:
            # Get customer_id for the bill
            c.execute("SELECT customer_id FROM billing WHERE id = ?", (bill_id,))
            customer_id = c.fetchone()[0]

            # Get current bill amount
            c.execute("SELECT amount FROM billing WHERE id = ?", (bill_id,))
            current_amount = c.fetchone()[0]

            # Calculate new credit and outstanding amounts
            new_credit = max(0, paid_amount - current_amount)
            new_outstanding = max(0, current_amount - paid_amount)

            # Update the bill
            c.execute("""
                UPDATE billing 
                SET paid_amount = ?, is_paid = ?, paid_date = ?, 
                    invoice_number = ?, paid_by = ?, status = ?,
                    credit_amount = ?, outstanding_amount = ?
                WHERE id = ?
            """, (paid_amount, is_paid, paid_date, invoice_number, paid_by, 
                  'Paid' if is_paid else 'Unpaid',
                  new_credit, new_outstanding, bill_id))
            
            # If payment is made, record in payment history
            if is_paid and paid_amount > 0:
                c.execute("""
                    INSERT INTO payment_history (
                        invoice_number, customer_id, payment_date, amount_paid,
                        credit_amount, outstanding_amount, paid_by, timestamp
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
                """, (invoice_number, customer_id, paid_date, paid_amount, 
                      new_credit, new_outstanding, paid_by))
            
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise
        finally:
            conn.close()

    def get_bills_by_customer(self, customer_id):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT * FROM billing WHERE customer_id = ?", (customer_id,))
        result = c.fetchall()
        conn.close()
        return result

    def get_all_bills(self):
        """Get all bills from the database for the billing manager"""
        conn = self.get_connection()
        c = conn.cursor()
        try:
            c.execute("""
                SELECT b.id, b.customer_id, b.month, b.year, b.amount, 
                       b.paid_amount, b.is_paid, c.name as customer_name
                FROM billing b
                LEFT JOIN customers c ON b.customer_id = c.id
                ORDER BY b.year DESC, b.month DESC
            """)
            return c.fetchall()
        finally:
            conn.close()

    # Customer purchase methods
    def add_customer_purchase(self, customer_id, product_id, billing_id, purchase_date):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("""
            INSERT INTO customer_purchases (customer_id, product_id, billing_id, purchase_date)
            VALUES (?, ?, ?, ?)
        """, (customer_id, product_id, billing_id, purchase_date))
        conn.commit()
        conn.close()

    def get_customer_purchases(self, customer_id):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT * FROM customer_purchases WHERE customer_id = ?", (customer_id,))
        result = c.fetchall()
        conn.close()
        return result

    # Stock management methods
    def update_stock(self, product_id, quantity_change, sold_change):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("""
            INSERT OR IGNORE INTO stock (product_id, quantity, sold) VALUES (?, ?, ?)
        """, (product_id, 0, 0))
        c.execute("""
            UPDATE stock 
            SET quantity = quantity + ?, sold = sold + ?
            WHERE product_id = ?
        """, (quantity_change, sold_change, product_id))
        conn.commit()
        conn.close()

    def get_stock(self, product_id):
        conn = self.get_connection()
        c = conn.cursor()
        c.execute("SELECT quantity, sold FROM stock WHERE product_id = ?", (product_id,))
        result = c.fetchone()
        conn.close()
        return result

    # Backup/restore methods
    def backup_database(self, backup_path):
        """Create a backup of the database"""
        def do_backup():
            if not os.path.exists(os.path.dirname(backup_path)):
                os.makedirs(os.path.dirname(backup_path))
            
            # Close any open connections to the database
            self.close_connection()
            
            # Wait a moment to ensure the file is released
            time.sleep(1)
            
            # Ensure no other connections are holding the database
            with sqlite3.connect(self.db_path, timeout=10) as src:
                src.execute("PRAGMA wal_checkpoint(FULL)")  # Ensure WAL is checkpointed
                with sqlite3.connect(backup_path) as dst:
                    src.backup(dst, pages=1, sleep=0.25)  # Backup with small chunks and sleep
            
            logging.info(f"Database backup created at {backup_path}")
            return True

        try:
            return self.execute_with_retry(do_backup)
        except Exception as e:
            logging.error(f"Backup failed: {str(e)}")
            return False

    def restore_database(self, restore_path):
        """Restore database from backup"""
        try:
            if not os.path.exists(restore_path):
                return False
                
            # Close any existing connections
            self.close_connection()
                
            # Make a backup of current database before restore
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{self.db_path}.pre_restore_{timestamp}.bak"
            self.backup_database(backup_name)
            
            # Restore from backup
            with sqlite3.connect(restore_path) as src:
                with sqlite3.connect(self.db_path) as dst:
                    src.backup(dst)
            
            logging.info(f"Database restored from {restore_path}")
            return True
        except Exception as e:
            logging.error(f"Restore failed: {str(e)}")
            return False

    def export_to_csv(self, export_dir):
        """Export all tables to CSV files"""
        try:
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)
                
            conn = self.get_connection()
            c = conn.cursor()
            
            # Get list of tables
            c.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [table[0] for table in c.fetchall()]
            
            for table in tables:
                if table == 'sqlite_sequence':
                    continue
                    
                # Get data
                c.execute(f"SELECT * FROM {table}")
                data = c.fetchall()
                
                # Get column names
                col_names = [description[0] for description in c.description]
                
                # Write to CSV
                csv_path = os.path.join(export_dir, f"{table}.csv")
                with open(csv_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(col_names)
                    writer.writerows(data)
            
            conn.close()
            logging.info(f"Exported database to CSV at {export_dir}")
            return True
        except Exception as e:
            logging.error(f"Export failed: {str(e)}")
            return False

    def export_to_excel(self, export_dir):
        """Export all tables to Excel files"""
        try:
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)
                
            conn = self.get_connection()
            c = conn.cursor()
            
            # Get list of tables
            c.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [table[0] for table in c.fetchall()]
            
            for table in tables:
                if table == 'sqlite_sequence':
                    continue
                    
                # Use pandas to read the table and export to Excel
                df = pd.read_sql_query(f"SELECT * FROM {table}", conn)
                excel_path = os.path.join(export_dir, f"{table}.xlsx")
                df.to_excel(excel_path, index=False)
            
            conn.close()
            logging.info(f"Exported database to Excel at {export_dir}")
            return True
        except Exception as e:
            logging.error(f"Export to Excel failed: {str(e)}")
            return False

    def import_from_csv(self, import_dir):
        """Import data from CSV files"""
        try:
            if not os.path.exists(import_dir):
                return False
                
            conn = self.get_connection()
            c = conn.cursor()
            
            # Get list of CSV files
            csv_files = [f for f in os.listdir(import_dir) if f.endswith('.csv')]
            
            for csv_file in csv_files:
                table_name = csv_file[:-4]  # Remove .csv extension
                
                # Check if table exists
                c.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                if not c.fetchone():
                    continue
                    
                # Read CSV data
                csv_path = os.path.join(import_dir, csv_file)
                with open(csv_path, 'r', newline='', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    columns = next(reader)  # Get column names
                    
                    # Prepare insert statement
                    placeholders = ', '.join(['?'] * len(columns))
                    insert_sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
                    
                    # Clear existing data
                    c.execute(f"DELETE FROM {table_name}")
                    
                    # Insert new data
                    for row in reader:
                        c.execute(insert_sql, row)
            
            conn.commit()
            conn.close()
            logging.info(f"Imported data from CSV at {import_dir}")
            return True
        except Exception as e:
            logging.error(f"Import failed: {str(e)}")
            return False

    def import_from_excel(self, import_dir):
        """Import data from Excel files"""
        try:
            if not os.path.exists(import_dir):
                return False
                
            conn = self.get_connection()
            c = conn.cursor()
            
            # Get list of Excel files
            excel_files = [f for f in os.listdir(import_dir) if f.endswith('.xlsx')]
            
            for excel_file in excel_files:
                table_name = excel_file[:-5]  # Remove .xlsx extension
                
                # Check if table exists
                c.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                if not c.fetchone():
                    continue
                    
                # Read Excel data
                excel_path = os.path.join(import_dir, excel_file)
                df = pd.read_excel(excel_path)
                
                # Clear existing data
                c.execute(f"DELETE FROM {table_name}")
                
                # Insert new data
                for _, row in df.iterrows():
                    columns = ', '.join(df.columns)
                    placeholders = ', '.join(['?'] * len(df.columns))
                    insert_sql = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
                    c.execute(insert_sql, tuple(row))
            
            conn.commit()
            conn.close()
            logging.info(f"Imported data from Excel at {import_dir}")
            return True
        except Exception as e:
            logging.error(f"Import from Excel failed: {str(e)}")
            return False

    def differential_backup(self, backup_path):
        """Create a differential backup of the database"""
        def do_differential_backup():
            if not os.path.exists(os.path.dirname(backup_path)):
                os.makedirs(os.path.dirname(backup_path))
                
            # Close any open connections
            self.close_connection()
                
            # Checkpoint WAL to ensure consistency
            with sqlite3.connect(self.db_path, timeout=10) as conn:
                conn.execute("PRAGMA wal_checkpoint(FULL)")
            
            # Copy the main database file
            shutil.copy2(self.db_path, backup_path)
            
            # Copy the WAL file if it exists
            wal_file = self.db_path + "-wal"
            if os.path.exists(wal_file):
                shutil.copy2(wal_file, backup_path + "-wal")
                
            logging.info(f"Differential backup created at {backup_path}")
            return True

        try:
            return self.execute_with_retry(do_differential_backup)
        except Exception as e:
            logging.error(f"Differential backup failed: {str(e)}")
            return False

    def apply_differential_backup(self, backup_path):
        """Apply a differential backup to the database"""
        try:
            if not os.path.exists(backup_path):
                return False
                
            # Make a backup of current database before applying differential
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{self.db_path}.pre_diff_restore_{timestamp}.bak"
            self.backup_database(backup_name)
            
            # Close any existing connections
            self.close_connection()
                
            # Copy the differential backup over the main database
            shutil.copy2(backup_path, self.db_path)
            
            # Copy the WAL file if it exists
            wal_file = backup_path + "-wal"
            if os.path.exists(wal_file):
                shutil.copy2(wal_file, self.db_path + "-wal")
                
            logging.info(f"Differential backup applied from {backup_path}")
            return True
        except Exception as e:
            logging.error(f"Apply differential backup failed: {str(e)}")
            return False

    def export_database(self, file_path):
        conn = self.get_connection()
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            for line in conn.iterdump():
                f.write(line + '\n')
        conn.close()

    def import_database(self, file_path):
        conn = self.get_connection()
        with open(file_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()
        conn.executescript(sql_script)
        conn.commit()
        conn.close()

    def get_payment_history(self, customer_id=None, start_date=None, end_date=None):
        """Get payment history with optional filters"""
        conn = self.get_connection()
        c = conn.cursor()
        try:
            query = """
                SELECT ph.*, c.name as customer_name, c.user_name
                FROM payment_history ph
                JOIN customers c ON ph.customer_id = c.id
                WHERE 1=1
            """
            params = []
            
            if customer_id:
                query += " AND ph.customer_id = ?"
                params.append(customer_id)
            
            if start_date:
                query += " AND ph.payment_date >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND ph.payment_date <= ?"
                params.append(end_date)
            
            query += " ORDER BY ph.timestamp DESC"
            
            c.execute(query, params)
            return c.fetchall()
        finally:
            conn.close()

    def get_customer_payment_history(self, customer_id):
        """Get payment history for a specific customer"""
        return self.get_payment_history(customer_id=customer_id)

    def get_payment_details(self, invoice_number):
        """Get detailed payment information for a specific invoice"""
        conn = self.get_connection()
        c = conn.cursor()
        try:
            c.execute("""
                SELECT ph.*, c.name as customer_name, c.user_name
                FROM payment_history ph
                JOIN customers c ON ph.customer_id = c.id
                WHERE ph.invoice_number = ?
            """, (invoice_number,))
            return c.fetchone()
        finally:
            conn.close()

    def use_imported_credit(self, customer_id, amount_needed):
        """
        Use imported credit (customers.credit_balance) for a customer.
        Returns (used_credit, remaining_amount_to_pay).
        """
        import sqlite3, os
        db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        c.execute("SELECT credit_balance FROM customers WHERE id = ?", (customer_id,))
        row = c.fetchone()
        credit_balance = row[0] if row and row[0] is not None else 0.0
        used_credit = min(credit_balance, amount_needed)
        if used_credit > 0:
            c.execute("UPDATE customers SET credit_balance = credit_balance - ? WHERE id = ?", (used_credit, customer_id))
            
            # If credit was fully utilized, clear all billing credit references
            new_credit = credit_balance - used_credit
            if new_credit <= 0:
                c.execute('''UPDATE billing 
                            SET credit_amount = 0
                            WHERE customer_id = ?''', (customer_id,))
            
            conn.commit()
        conn.close()
        return used_credit, amount_needed - used_credit

    def use_imported_outstanding(self, customer_id, amount_needed):
        """
        Use imported outstanding (customers.outstanding_amount) for a customer.
        Returns (used_outstanding, remaining_amount_to_pay).
        """
        import sqlite3, os
        db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        c.execute("SELECT outstanding_amount FROM customers WHERE id = ?", (customer_id,))
        row = c.fetchone()
        outstanding_amount = row[0] if row and row[0] is not None else 0.0
        used_outstanding = min(outstanding_amount, amount_needed)
        if used_outstanding > 0:
            c.execute("UPDATE customers SET outstanding_amount = outstanding_amount - ? WHERE id = ?", (used_outstanding, customer_id))
            conn.commit()
        conn.close()
        return used_outstanding, amount_needed - used_outstanding

    def clear_customer_credit_references(self, customer_id):
        """
        Clear all credit references for a customer when credit is fully utilized.
        This prevents carry forward to future bills.
        """
        import sqlite3, os
        db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        
        try:
            # Clear credit_amount from all billing records for this customer
            c.execute('''UPDATE billing 
                        SET credit_amount = 0
                        WHERE customer_id = ?''', (customer_id,))
            
            # Ensure customer credit balance is zero
            c.execute('''UPDATE customers 
                        SET credit_balance = 0
                        WHERE id = ?''', (customer_id,))
            
            conn.commit()
            logging.info(f"Cleared all credit references for customer {customer_id}")
        except Exception as e:
            logging.error(f"Error clearing credit references for customer {customer_id}: {str(e)}")
            conn.rollback()
        finally:
            conn.close()

    def check_and_clear_zero_credit_customers(self):
        """
        Check for customers with zero credit balance and clear any residual credit references.
        This should be called during month-end processing or system maintenance.
        """
        import sqlite3, os
        db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        
        try:
            # Find customers with zero credit balance but non-zero billing credit
            c.execute('''
                SELECT DISTINCT c.id, c.user_name
                FROM customers c
                JOIN billing b ON c.id = b.customer_id
                WHERE c.credit_balance = 0 
                AND b.credit_amount > 0
            ''')
            
            zero_credit_customers = c.fetchall()
            
            for customer_id, user_name in zero_credit_customers:
                # Clear all credit references for this customer
                c.execute('''UPDATE billing 
                            SET credit_amount = 0
                            WHERE customer_id = ?''', (customer_id,))
                logging.info(f"Cleared residual credit for customer {user_name} (ID: {customer_id})")
            
            conn.commit()
            return len(zero_credit_customers)
            
        except Exception as e:
            logging.error(f"Error checking zero credit customers: {str(e)}")
            conn.rollback()
            return 0
        finally:
            conn.close()

    def prevent_credit_carry_forward(self, customer_id):
        """
        Prevent credit carry forward for a specific customer.
        This method ensures that when credit is fully utilized, no residual credit appears in future bills.
        """
        import sqlite3, os
        db_path = os.path.join(os.getenv('APPDATA'), 'CRM_System', 'crm_database.db')
        conn = sqlite3.connect(db_path)
        c = conn.cursor()
        
        try:
            # Get current credit balance
            c.execute("SELECT credit_balance FROM customers WHERE id = ?", (customer_id,))
            result = c.fetchone()
            current_credit = result[0] if result and result[0] is not None else 0.0
            
            # If credit is zero or negative, clear all credit references
            if current_credit <= 0:
                c.execute('''UPDATE billing 
                            SET credit_amount = 0
                            WHERE customer_id = ?''', (customer_id,))
                
                # Ensure customer credit balance is exactly zero
                c.execute('''UPDATE customers 
                            SET credit_balance = 0
                            WHERE id = ?''', (customer_id,))
                
                conn.commit()
                logging.info(f"Prevented credit carry forward for customer {customer_id}")
                return True
            
            return False
            
        except Exception as e:
            logging.error(f"Error preventing credit carry forward for customer {customer_id}: {str(e)}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def threadsafe_execute(self, query, params=()):
        """Execute a query with automatic retry on lock, thread-safe."""
        for attempt in range(5):
            with self.lock:
                try:
                    with sqlite3.connect(self.db_path, timeout=30) as conn:
                        conn.execute("PRAGMA busy_timeout = 30000")  # 30 second timeout
                        conn.execute("PRAGMA journal_mode = WAL")  # Better concurrency
                        cursor = conn.cursor()
                        cursor.execute(query, params)
                        conn.commit()
                        if query.strip().upper().startswith('SELECT'):
                            return cursor.fetchall()
                        return None
                except sqlite3.OperationalError as e:
                    if "database is locked" in str(e) and attempt < 4:
                        time.sleep(0.1 * (attempt + 1))  # Exponential backoff
                        continue
                    raise

    def _optimize_database_settings(self):
        """Set optimal database settings for concurrent access"""
        try:
            with self.get_connection() as conn:
                conn.execute("PRAGMA journal_mode = WAL")
                conn.execute("PRAGMA synchronous = NORMAL")
                conn.execute("PRAGMA busy_timeout = 30000")  # 30 second timeout
                conn.execute("PRAGMA foreign_keys = ON")
        except Exception as e:
            logging.error(f"Error optimizing database settings: {str(e)}")

    def get_connection(self):
        """Get a database connection with extended timeout"""
        return sqlite3.connect(self.db_path, timeout=30, isolation_level=None)

    def begin_exclusive_transaction(self):
        """Begin an exclusive transaction"""
        conn = self.get_connection()
        conn.execute("BEGIN EXCLUSIVE TRANSACTION")
        return conn

    def commit_transaction(self, conn):
        """Commit and close a transaction"""
        try:
            conn.commit()
        finally:
            conn.close()

    def rollback_transaction(self, conn):
        """Rollback and close a transaction"""
        try:
            conn.rollback()
        finally:
            conn.close()

    def execute_in_transaction(self, query, params=None):
        """Execute a single query in a transaction"""
        conn = None
        try:
            conn = self.begin_exclusive_transaction()
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            conn.commit()
            return cursor
        except Exception as e:
            if conn:
                self.rollback_transaction(conn)
            raise e
        finally:
            if conn:
                conn.close()

    def sync_customer_financial_data(self, customer_id, preserve_imported_values=False):
        """Synchronize customer financial data after changes"""
        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            if preserve_imported_values:
                # For imported data, only update from billing records if the customer table values are zero
                cursor.execute('''
                    UPDATE customers 
                    SET outstanding_amount = CASE 
                        WHEN outstanding_amount = 0 THEN (
                            SELECT COALESCE(SUM(amount - COALESCE(paid_amount, 0)), 0)
                            FROM billing 
                            WHERE customer_id = ? AND status != 'Paid'
                        )
                        ELSE outstanding_amount
                    END,
                    credit_balance = CASE
                        WHEN credit_balance = 0 THEN (
                            SELECT COALESCE(MAX(credit_amount), 0)
                            FROM billing
                            WHERE customer_id = ? AND status = 'Paid'
                            ORDER BY year DESC, month DESC
                            LIMIT 1
                        )
                        ELSE credit_balance
                    END
                    WHERE id = ?
                ''', (customer_id, customer_id, customer_id))
            else:
                # For normal operations, always sync from billing records
                cursor.execute('''
                    UPDATE customers 
                    SET outstanding_amount = (
                        SELECT COALESCE(SUM(amount - COALESCE(paid_amount, 0)), 0)
                        FROM billing 
                        WHERE customer_id = ? AND status != 'Paid'
                    ),
                    credit_balance = (
                        SELECT COALESCE(MAX(credit_amount), 0)
                        FROM billing
                        WHERE customer_id = ? AND status = 'Paid'
                        ORDER BY year DESC, month DESC
                        LIMIT 1
                    )
                    WHERE id = ?
                ''', (customer_id, customer_id, customer_id))
            conn.commit()
        except Exception as e:
            logging.error(f"Error syncing financial data for customer {customer_id}: {str(e)}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()

    def _setup_database(self):
        conn = self._create_connection()
        try:
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA busy_timeout=30000")
            conn.execute("PRAGMA foreign_keys=ON")
            conn.execute("PRAGMA wal_autocheckpoint=1000")
            conn.commit()
        finally:
            conn.close()
    
    def _create_connection(self) -> sqlite3.Connection:
        return sqlite3.connect(
            self.db_path,
            timeout=30,
            isolation_level=None,
            check_same_thread=False
        )
    
    def execute_transaction(self, query: str, params=None, operation_name: str = "Unnamed operation"):
        conn = None
        thread_id = threading.get_ident()
        try:
            with self.transaction_lock:
                if thread_id in self.active_transactions:
                    raise RuntimeError(f"Nested transaction detected in thread {thread_id}")
                self.active_transactions[thread_id] = {
                    'start_time': time.time(),
                    'operation': operation_name
                }
            conn = self._create_connection()
            cursor = conn.cursor()
            cursor.execute("BEGIN IMMEDIATE TRANSACTION")
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            results = cursor.fetchall()
            conn.commit()
            return (True, results)
        except sqlite3.Error as e:
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            logging.error(f"Transaction failed ({operation_name}): {str(e)}")
            return (False, None)
        finally:
            if conn:
                conn.close()
            with self.transaction_lock:
                self.active_transactions.pop(thread_id, None)
    
    def execute_in_transaction(self, operations: list, operation_name: str = "Batch operation"):
        conn = None
        thread_id = threading.get_ident()
        results = []
        try:
            with self.transaction_lock:
                if thread_id in self.active_transactions:
                    raise RuntimeError(f"Nested transaction detected in thread {thread_id}")
                self.active_transactions[thread_id] = {
                    'start_time': time.time(),
                    'operation': operation_name
                }
            conn = self._create_connection()
            cursor = conn.cursor()
            cursor.execute("BEGIN IMMEDIATE TRANSACTION")
            for query, params in operations:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                results.append(cursor.fetchall())
            conn.commit()
            return (True, results)
        except sqlite3.Error as e:
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            logging.error(f"Batch transaction failed ({operation_name}): {str(e)}")
            return (False, None)
        finally:
            if conn:
                conn.close()
            with self.transaction_lock:
                self.active_transactions.pop(thread_id, None)
    
    def transaction(self, operation_name: str = "Custom operation"):
        def decorator(func: Callable):
            def wrapper(*args, **kwargs):
                conn = None
                thread_id = threading.get_ident()
                try:
                    with self.transaction_lock:
                        if thread_id in self.active_transactions:
                            raise RuntimeError(f"Nested transaction detected in thread {thread_id}")
                        self.active_transactions[thread_id] = {
                            'start_time': time.time(),
                            'operation': operation_name
                        }
                    conn = self._create_connection()
                    cursor = conn.cursor()
                    cursor.execute("BEGIN IMMEDIATE TRANSACTION")
                    result = func(conn, cursor, *args, **kwargs)
                    conn.commit()
                    return result
                except sqlite3.Error as e:
                    if conn:
                        try:
                            conn.rollback()
                        except:
                            pass
                    logging.error(f"Decorated transaction failed ({operation_name}): {str(e)}")
                    raise
                finally:
                    if conn:
                        conn.close()
                    with self.transaction_lock:
                        self.active_transactions.pop(thread_id, None)
            return wrapper
        return decorator
    
    def get_active_transactions(self):
        with self.transaction_lock:
            return {
                tid: {
                    'operation': info['operation'],
                    'duration': time.time() - info['start_time']
                }
                for tid, info in self.active_transactions.items()
            }
    
    def vacuum(self):
        success, _ = self.execute_transaction("VACUUM", operation_name="Database VACUUM")
        return success

class BillingManager:
    def __init__(self, parent):
        self.parent = parent
        self.db = Database()
        
        # Initialize the treeview widget
        self.tree = ttk.Treeview(parent)
        self.tree.pack(expand=True, fill='both', padx=10, pady=10)
        
        # Configure columns
        self._setup_columns()
        
        # Load initial data
        self.load_bills()

    def _setup_columns(self):
        """Configure treeview columns and headings"""
        self.tree['columns'] = ('ID', 'Customer', 'Month', 'Year', 'Amount', 'Paid', 'Status')
        
        # Format columns
        self.tree.column('#0', width=0, stretch=tk.NO)
        self.tree.column('ID', anchor=tk.W, width=50)
        self.tree.column('Customer', anchor=tk.W, width=150)
        self.tree.column('Month', anchor=tk.CENTER, width=80)
        self.tree.column('Year', anchor=tk.CENTER, width=80)
        self.tree.column('Amount', anchor=tk.E, width=100)
        self.tree.column('Paid', anchor=tk.E, width=100)
        self.tree.column('Status', anchor=tk.CENTER, width=100)
        
        # Create headings
        self.tree.heading('ID', text='ID', anchor=tk.W)
        self.tree.heading('Customer', text='Customer', anchor=tk.W)
        self.tree.heading('Month', text='Month', anchor=tk.CENTER)
        self.tree.heading('Year', text='Year', anchor=tk.CENTER)
        self.tree.heading('Amount', text='Amount', anchor=tk.E)
        self.tree.heading('Paid', text='Paid', anchor=tk.E)
        self.tree.heading('Status', text='Status', anchor=tk.CENTER)

    def load_bills(self):
        """Load bills from database into the treeview"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # Get bills from database
        bills = self.db.get_all_bills()
        
        # Populate treeview
        for bill in bills:
            bill_id, customer_id, month, year, amount, paid_amount, is_paid, customer_name = bill
            
            status = "Paid" if is_paid else "Unpaid"
            
            self.tree.insert('', 'end', values=(
                bill_id, 
                customer_name,
                month,
                year,
                f"${amount:.2f}",
                f"${paid_amount:.2f}",
                status
            ))

class GoogleDriveConfigWizard:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Google Drive Configuration Wizard")
        self.root.geometry("700x500")
        self.root.resizable(False, False)
        
        self.COLORS = {
            'background': '#F5F5F5',
            'card_bg': '#FFFFFF',
            'primary_accent': '#2D3748',
            'secondary_accent': '#48BB78',
            'text_primary': '#1A202C',
            'text_secondary': '#718096',
            'button_start': '#4A5568',
            'button_end': '#2D3748',
            'border': '#E2E8F0'
        }
        
        self.root.configure(bg=self.COLORS['background'])
        self.credentials_path = tk.StringVar()
        self.token_path = tk.StringVar()
        self.result = None
        self.credentials_entry = None
        self.token_entry = None

    def create_ui(self):
        main_container = tk.Frame(self.root, bg=self.COLORS['background'])
        main_container.pack(fill="both", expand=True)

        canvas = tk.Canvas(main_container, bg=self.COLORS['background'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.COLORS['background'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=(20, 0), pady=20)
        scrollbar.pack(side="right", fill="y")

        main_frame = tk.Frame(scrollable_frame, bg=self.COLORS['background'])
        main_frame.pack(fill="both", expand=True, padx=30, pady=30)

        tk.Label(
            main_frame,
            text="Google Drive Setup Wizard",
            font=("Helvetica", 18, "bold"),
            bg=self.COLORS['background'],
            fg=self.COLORS['text_primary']
        ).pack(pady=(0, 20))

        tk.Label(
            main_frame,
            text="To use cloud backup, we need to set up Google Drive access.\nPlease follow these steps to obtain your credentials:",
            font=("Helvetica", 12),
            bg=self.COLORS['background'],
            fg=self.COLORS['text_secondary'],
            justify="left"
        ).pack(pady=(0, 15), anchor="w")

        instructions = [
            "1. Go to Google Cloud Console (console.cloud.google.com)",
            "2. Create a new project or select an existing one",
            "3. Enable the Google Drive API",
            "4. Create OAuth 2.0 credentials",
            "5. Download the credentials JSON file"
        ]
        
        for instruction in instructions:
            tk.Label(
                main_frame,
                text=instruction,
                font=("Helvetica", 11),
                bg=self.COLORS['background'],
                fg=self.COLORS['text_secondary'],
                justify="left"
            ).pack(anchor="w", pady=2)

        form_frame = tk.Frame(main_frame, bg=self.COLORS['background'])
        form_frame.pack(fill="x", pady=10)

        tk.Label(
            form_frame,
            text="Credentials JSON File Path:",
            font=("Helvetica", 12, "bold"),
            bg=self.COLORS['background'],
            fg=self.COLORS['text_primary']
        ).pack(anchor="w")
        
        creds_path_frame = tk.Frame(form_frame, bg=self.COLORS['background'])
        creds_path_frame.pack(fill="x", pady=(5, 15))
        
        self.credentials_entry = tk.Entry(
            creds_path_frame,
            textvariable=self.credentials_path,
            width=45,
            bd=1,
            relief="solid"
        )
        self.credentials_entry.pack(side="left")
        
        tk.Button(
            creds_path_frame,
            text="Browse",
            command=self._browse_credentials,
            bg=self.COLORS['button_start'],
            fg="white",
            font=("Helvetica", 10, "bold"),
            relief="flat",
            activebackground=self.COLORS['button_end'],
            width=10
        ).pack(side="left", padx=10)

        tk.Label(
            form_frame,
            text="Token Storage Path (where to save the token):",
            font=("Helvetica", 12, "bold"),
            bg=self.COLORS['background'],
            fg=self.COLORS['text_primary']
        ).pack(anchor="w")
        
        path_frame = tk.Frame(form_frame, bg=self.COLORS['background'])
        path_frame.pack(fill="x", pady=(5, 15))
        
        self.token_entry = tk.Entry(
            path_frame,
            textvariable=self.token_path,
            width=50,
            bd=1,
            relief="solid"
        )
        self.token_entry.pack(side="left")
        
        tk.Button(
            path_frame,
            text="Browse",
            command=self._browse_token,
            bg=self.COLORS['button_start'],
            fg="white",
            font=("Helvetica", 10, "bold"),
            relief="flat",
            activebackground=self.COLORS['button_end'],
            width=10
        ).pack(side="left", padx=10)

        button_frame = tk.Frame(main_frame, bg=self.COLORS['background'])
        button_frame.pack(fill="x", pady=20)

        tk.Button(
            button_frame,
            text="Cancel",
            command=self.root.destroy,
            bg=self.COLORS['button_start'],
            fg="white",
            font=("Helvetica", 12, "bold"),
            relief="flat",
            activebackground=self.COLORS['button_end'],
            width=15
        ).pack(side="right", padx=10)

        tk.Button(
            button_frame,
            text="Configure",
            command=self._configure,
            bg=self.COLORS['secondary_accent'],
            fg="white",
            font=("Helvetica", 12, "bold"),
            relief="flat",
            activebackground='#38A169',
            width=15
        ).pack(side="right")

    def _browse_credentials(self):
        path = filedialog.askopenfilename(filetypes=[("JSON files", "*.json")])
        if path:
            self.credentials_path.set(path)
            self.credentials_entry.delete(0, tk.END)
            self.credentials_entry.insert(0, path)

    def _browse_token(self):
        path = filedialog.askdirectory()
        if path:
            token_path = os.path.join(path, "token.json")
            self.token_path.set(token_path)
            self.token_entry.delete(0, tk.END)
            self.token_entry.insert(0, token_path)

    def _configure(self):
        credentials_path = self.credentials_path.get().strip()
        token_path = self.token_path.get().strip()

        if not credentials_path or not token_path:
            messagebox.showerror("Error", "Please select both the credentials file and token path")
            return

        try:
            with open(credentials_path, 'rb') as f:
                credentials = json.load(f)

            if "installed" not in credentials:
                messagebox.showerror("Error", "Invalid credentials JSON file: 'installed' key not found")
                return

            client_id = credentials["installed"].get("client_id")
            client_secret = credentials["installed"].get("client_secret")

            if not client_id or not client_secret:
                messagebox.showerror("Error", "Invalid credentials file: client_id or client_secret missing")
                return

            creds_path = os.path.join(os.path.dirname(token_path), "client_secret.json")
            with open(creds_path, 'w') as f:
                json.dump(credentials, f)

            flow = InstalledAppFlow.from_client_secrets_file(
                creds_path, ['https://www.googleapis.com/auth/drive']
            )
            creds = flow.run_local_server(port=0)

            with open(token_path, 'w') as f:
                json.dump(creds.to_json(), f)

            self.result = (client_id, client_secret, token_path)
            messagebox.showinfo("Success", "Google Drive configuration successful!")
            self.root.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to configure Google Drive: {str(e)}")

    def run(self):
        self.create_ui()
        self.root.mainloop()
        return self.result
