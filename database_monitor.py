import threading
import time
import logging
from typing import Dict

class DatabaseMonitor:
    def __init__(self, db, check_interval=10):
        self.db = db
        self.check_interval = check_interval
        self._stop_event = threading.Event()
        self._thread = None
        
    def start(self):
        if self._thread is not None:
            return
        self._stop_event.clear()
        self._thread = threading.Thread(
            target=self._monitor_loop,
            daemon=True
        )
        self._thread.start()
        logging.info("Database monitor started")
        
    def stop(self):
        if self._thread is None:
            return
        self._stop_event.set()
        self._thread.join()
        self._thread = None
        logging.info("Database monitor stopped")
        
    def _monitor_loop(self):
        warning_threshold = 5  # seconds
        critical_threshold = 30  # seconds
        while not self._stop_event.is_set():
            try:
                transactions = self.db.get_active_transactions()
                now = time.time()
                for tid, info in transactions.items():
                    duration = info['duration']
                    if duration > critical_threshold:
                        logging.error(
                            f"CRITICAL: Long-running transaction (Thread {tid}): "
                            f"{info['operation']} running for {duration:.1f} seconds"
                        )
                    elif duration > warning_threshold:
                        logging.warning(
                            f"WARNING: Long transaction (Thread {tid}): "
                            f"{info['operation']} running for {duration:.1f} seconds"
                        )
            except Exception as e:
                logging.error(f"Monitoring error: {str(e)}")
            self._stop_event.wait(self.check_interval) 
