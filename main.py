import tkinter as tk
from tkinter import messagebox, ttk
import datetime
import os
import sys
from PIL import Image, ImageTk
import logging
import threading
import time

# Add project directory to sys.path to ensure module resolution
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from license_manager import LicenseManager
except ImportError as e:
    logging.error(f"Failed to import LicenseManager: {str(e)}")
    raise

try:
    from database import Database
except ImportError as e:
    logging.error(f"Failed to import Database: {str(e)}")
    raise

def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    path = os.path.join(base_path, relative_path)
    path = os.path.normpath(path)  # Normalize path for different OS
    
    # Debugging - verify the path exists
    if not os.path.exists(path):
        logging.warning(f"Resource path not found: {path}")
    
    return path

def center_window(window, width, height):
    """Center window on screen with specified dimensions"""
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    window.geometry(f"{width}x{height}+{x}+{y}")
    window.update_idletasks()

class App(tk.Frame):
    def __init__(self, parent, license_mgr):
        super().__init__(parent)
        self.parent = parent
        self.license_mgr = license_mgr
        self.parent.title("CRM System")
        self.parent.geometry("1922x1080")
        self.parent.configure(bg='#F8F9FA')
        self.parent.state('zoomed')  # Maximize window

        # Define color scheme to match login.py and license_manager.py
        self.COLORS = {
            'background': '#F8F9FA',
            'card_bg': '#FFFFFF',
            'primary_accent': '#4A6FA5',
            'secondary_accent': '#6C8FC7',
            'text_primary': '#2D3748',
            'text_secondary': '#718096',
            'button_start': '#4A6FA5',
            'button_end': '#3A5A8C',
            'transparent': 'SystemTransparent',
            'warning': '#E53E3E',
            'input_bg': '#EDF2F7',
            'border': '#E2E8F0'
        }

        # Set favicon with resource_path
        self.set_favicon()

        # Initialize the database
        self.db = Database()
        self.db_path = self.db.db_path

        # Setup navigation commands
        self._setup_navigation()

        self.container = tk.Frame(self, bg=self.COLORS['background'])
        self.container.pack(fill="both", expand=True)

        self.current_page = None
        self.billing_page = None
        self.splash = None

        logging.info("Application initialized, showing login page")
        self.show_login()

    def set_favicon(self):
        """Try multiple possible favicon locations using resource_path"""
        icon_paths = [
            resource_path(os.path.join('assets', 'dashboard', 'CRM.png')),
            resource_path(os.path.join('assets', 'app_icon.ico')),
            # Fallback paths
            resource_path('CRM.png'),
            resource_path('assets/CRM.png')
        ]
        
        for icon_path in icon_paths:
            try:
                if os.path.exists(icon_path):
                    img = Image.open(icon_path)
                    self.favicon = ImageTk.PhotoImage(img)
                    self.parent.iconphoto(True, self.favicon)
                    logging.info(f"Successfully loaded favicon from {icon_path}")
                    break
                else:
                    logging.warning(f"Favicon path does not exist: {icon_path}")
            except Exception as e:
                logging.warning(f"Couldn't load favicon from {icon_path}: {str(e)}")

    def _setup_navigation(self):
        """Initialize all navigation commands"""
        self.nav_commands = {
            'show_login': self.show_login,
            'show_register': self.show_register,
            'show_forgot_password': self.show_forgot_password,
            'show_email_config': self.show_email_config,
            'show_dashboard': self.show_dashboard,
            'show_customers': self.show_customers,
            'show_billing': self.show_billing,
            'show_packages': self.show_packages,
            'show_products': self.show_products,
            'show_regions': self.show_regions,
            'show_stock': self.show_stock,
            'show_backup_config': self.show_backup_config,
            'show_backup_restore': self.show_backup_restore,
            'refresh_billing': self.refresh_billing,
            'refresh_stock': self.refresh_stock,
            'refresh_customers': self.refresh_customers
        }

    def _clear_frame(self):
        """Clear the current page"""
        if self.current_page:
            self.current_page.destroy()
        for widget in self.container.winfo_children():
            widget.destroy()

    def _show_error_message(self, title: str, message: str):
        """Show error message with styling matching login.py and license_manager.py"""
        dialog = tk.Toplevel(self.parent)
        dialog.title(title)
        dialog.configure(bg=self.COLORS['background'])
        dialog.transient(self.parent)
        dialog.grab_set()
        
        # Center the dialog
        dialog_width = 350
        dialog_height = 200
        screen_width = self.parent.winfo_screenwidth()
        screen_height = self.parent.winfo_screenheight()
        x = (screen_width - dialog_width) // 2
        y = (screen_height - dialog_height) // 2
        dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        
        # Main frame
        main_frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], padx=20, pady=20)
        main_frame.pack(fill="both", expand=True)

        # Error icon
        tk.Label(
            main_frame,
            text="⚠",
            font=("Helvetica", 24),
            fg=self.COLORS['warning'],
            bg=self.COLORS['card_bg']
        ).pack(pady=(0, 10))

        # Error message
        tk.Label(
            main_frame,
            text=message,
            font=("Helvetica", 12),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            wraplength=300
        ).pack(pady=(0, 20))

        # OK button
        tk.Button(
            main_frame,
            text="OK",
            font=("Helvetica", 12, "bold"),
            bg=self.COLORS['primary_accent'],
            fg="#FFFFFF",
            activebackground=self.COLORS['secondary_accent'],
            activeforeground="#FFFFFF",
            width=15,
            height=1,
            bd=0,
            relief="flat",
            command=lambda: dialog.destroy()
        ).pack()

        dialog.wait_window()

    def show_splash_during_transition(self, next_action, message="Loading..."):
        """Show splash screen during page transitions"""
        if self.splash:
            self.splash.destroy()
        
        self.splash, progress = show_splash(self.parent, message)
        self.parent.after(1000, lambda: self._complete_transition(next_action, self.splash))
    
    def _complete_transition(self, next_action, splash):
        """Complete the transition after showing splash"""
        splash.destroy()
        self.splash = None
        next_action()

    def show_login(self):
        """Show login page with splash screen"""
        logging.debug("Showing login page")
        self.show_splash_during_transition(self._load_login_page, "Loading Login Page...")
    
    def _load_login_page(self):
        try:
            from login import LoginPage
            self._clear_frame()
            self.current_page = LoginPage(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
            logging.info("Login page loaded successfully")
            # Schedule license expiry check after login, if license_mgr exists
            if self.license_mgr:
                self.license_mgr.check_expiry_reminder(self.parent)
        except ImportError as e:
            error_msg = f"Failed to import LoginPage: {str(e)}"
            logging.error(error_msg)
            self._show_error_message("Error", error_msg)
        except Exception as e:
            error_msg = f"Failed to load login page: {str(e)}"
            logging.error(error_msg)
            self._show_error_message("Error", error_msg)

    def show_register(self):
        """Show registration page with splash screen"""
        logging.debug("Showing register page")
        self.show_splash_during_transition(self._load_register_page, "Loading Registration Page...")
    
    def _load_register_page(self):
        try:
            from views.register import Register
            self._clear_frame()
            self.current_page = Register(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load register page: {str(e)}")
            logging.error(f"Register page error: {str(e)}")

    def show_forgot_password(self):
        """Show password recovery page with splash screen"""
        logging.debug("Showing forgot password page")
        self.show_splash_during_transition(self._load_forgot_password_page, "Loading Password Recovery...")
    
    def _load_forgot_password_page(self):
        try:
            from views.forgot_password import ForgotPassword
            self._clear_frame()
            self.current_page = ForgotPassword(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load forgot password page: {str(e)}")
            logging.error(f"Forgot password page error: {str(e)}")

    def show_email_config(self):
        """Show email configuration page"""
        logging.debug("Showing email config page")
        try:
            from views.email_config import EmailConfig
            self._clear_frame()
            self.current_page = EmailConfig(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load email config page: {str(e)}")
            logging.error(f"Email config page error: {str(e)}")

    def show_dashboard(self):
        """Show main dashboard with splash screen"""
        logging.debug("Showing dashboard page")
        self.show_splash_during_transition(self._load_dashboard_page, "Loading Dashboard...")
    
    def _load_dashboard_page(self):
        try:
            from views.dashboard import Dashboard
            self._clear_frame()
            self.current_page = Dashboard(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
            # Schedule license expiry check after dashboard load
            if self.license_mgr:
                self.license_mgr.check_expiry_reminder(self.parent)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load dashboard: {str(e)}")
            logging.error(f"Dashboard error: {str(e)}")

    def show_customers(self):
        """Show customer management page with splash screen"""
        logging.debug("Showing customers page")
        self.show_splash_during_transition(self._load_customers_page, "Loading Customer Management...")
    
    def _load_customers_page(self):
        try:
            from views.customers import CustomerManager
            self._clear_frame()
            self.current_page = CustomerManager(self.container, self.nav_commands)
            self.customers_page = self.current_page  # Store reference to customers page
            self.current_page.pack(fill="both", expand=True)
            logging.info("Customer management page loaded successfully")
        except ImportError as e:
            error_msg = f"Failed to import CustomerManager: {str(e)}"
            logging.error(error_msg)
            self._show_error_message("Error", error_msg)

    def show_billing(self, customer_id=None):
        """Show billing page with splash screen"""
        logging.debug(f"Showing billing page for customer: {customer_id}")
        self.show_splash_during_transition(
            lambda: self._load_billing_page(customer_id), 
            "Loading Billing Management..."
        )
    
    def _load_billing_page(self, customer_id=None):
        try:
            from views.billing import BillingManager
            self._clear_frame()
            self.current_page = BillingManager(self.container, self.nav_commands, customer_id=customer_id)
            self.billing_page = self.current_page
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load billing page: {str(e)}")
            logging.error(f"Billing page error: {str(e)}")

    def show_packages(self):
        """Show packages page"""
        logging.debug("Showing packages page")
        try:
            from views.packages import PackageManager
            self._clear_frame()
            self.current_page = PackageManager(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load packages page: {str(e)}")
            logging.error(f"Packages page error: {str(e)}")

    def show_products(self):
        """Show products page"""
        logging.debug("Showing products page")
        try:
            from views.products import ProductManager
            self._clear_frame()
            self.current_page = ProductManager(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load products page: {str(e)}")
            logging.error(f"Products page error: {str(e)}")

    def show_regions(self):
        """Show regions page"""
        logging.debug("Showing regions page")
        try:
            from views.regions import RegionManager
            self._clear_frame()
            self.current_page = RegionManager(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load regions page: {str(e)}")
            logging.error(f"Regions page error: {str(e)}")

    def show_stock(self):
        """Show stock page"""
        logging.debug("Showing stock page")
        try:
            from views.stock import StockManager
            self._clear_frame()
            self.current_page = StockManager(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load stock page: {str(e)}")
            logging.error(f"Stock page error: {str(e)}")

    def show_backup_config(self):
        """Show backup config page"""
        logging.debug("Showing backup config page")
        try:
            from backup_config import BackupConfig
            self._clear_frame()
            self.current_page = BackupConfig(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load backup config page: {str(e)}")
            logging.error(f"Backup config page error: {str(e)}")

    def show_backup_restore(self):
        """Show backup/restore page"""
        logging.debug("Showing backup/restore page")
        try:
            from views.backup_restore import BackupRestore
            self._clear_frame()
            self.current_page = BackupRestore(self.container, self.nav_commands)
            self.current_page.pack(fill="both", expand=True)
        except Exception as e:
            self._show_error_message("Error", f"Failed to load backup/restore page: {str(e)}")
            logging.error(f"Backup/restore page error: {str(e)}")

    def refresh_billing(self):
        """Refresh billing page"""
        logging.debug("Refreshing billing page")
        if self.billing_page:
            customer_id = getattr(self.billing_page, 'customer_id', None)
            self.show_billing(customer_id=customer_id)

    def refresh_stock(self):
        """Refresh stock page"""
        logging.debug("Refreshing stock page")
        if hasattr(self.current_page, 'refresh_stock'):
            self.current_page.refresh_stock()

    def refresh_customers(self):
        """Refresh customers page"""
        logging.debug("Refreshing customers page")
        if hasattr(self, 'customers_page') and self.customers_page:
            self.customers_page.refresh_customers()
        else:
            self.show_customers()

    def on_register_again(self):
        """Callback for license re-registration, navigates to login page and prompts for license key"""
        logging.info("Initiating re-registration process")
        self.show_login()

def setup_logging():
    """Configure logging for license validation"""
    log_dir = os.path.join(os.getenv('APPDATA', ''), 'CRM_System')
    os.makedirs(log_dir, exist_ok=True)
    log_path = os.path.join(log_dir, 'crm_license.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_path),
            logging.StreamHandler()
        ])

def show_splash(root, message="Loading..."):
    """Show a splash screen with customizable message"""
    splash = tk.Toplevel(root)
    splash.overrideredirect(True)
    
    # Use absolute path for the logo
    if getattr(sys, 'frozen', False):
        base_path = sys._MEIPASS
    else:
        base_path = os.path.dirname(os.path.abspath(__file__))
    logo_path = os.path.join(base_path, 'assets', 'Logo.png')
    if not os.path.exists(logo_path):
        logo_path = r'D:\CRM_System\assets\Logo.png'
    
    img = Image.open(logo_path)
    img = img.resize((200, 200))
    photo = ImageTk.PhotoImage(img)
    
    # Create a frame with fixed width/height
    splash_frame = tk.Frame(splash, bg='#F8F9FA', width=400, height=350)
    splash_frame.pack(fill="both", expand=True)
    splash_frame.pack_propagate(False)  # Prevent shrinking
    
    label = tk.Label(splash_frame, image=photo, bg='#F8F9FA')
    label.image = photo
    label.pack(expand=True, pady=(30, 10))
    
    loading = tk.Label(splash_frame, text=message, font=("Helvetica", 14), bg='#F8F9FA')
    loading.pack()
    
    # Add a progress bar
    progress = ttk.Progressbar(splash_frame, mode='indeterminate', length=300)
    progress.pack(pady=20)
    progress.start(15)  # Start the progress animation
    
    splash.configure(bg='#F8F9FA')
    splash.update_idletasks()
    
    # Center on screen
    w = 400
    h = 350
    ws = splash.winfo_screenwidth()
    hs = splash.winfo_screenheight()
    x = (ws // 2) - (w // 2)
    y = (hs // 2) - (h // 2)
    splash.geometry(f"{w}x{h}+{x}+{y}")
    
    # Make sure splash is on top
    splash.attributes('-topmost', True)
    splash.update()
    
    return splash, progress

def start_main_app(root, splash, progress):
    """Start the main application after splash screen"""
    # Simulate loading time
    for i in range(100):
        time.sleep(0.02)
        root.update_idletasks()
    
    splash.destroy()
    # Set window to use full available space
    root.state('zoomed')  # For Windows to maximize
    # Set minimum size to ensure proper layout
    root.minsize(1024, 768)
    
    app = App(root, None)
    app.pack(fill="both", expand=True)
    # Now create the license manager and set the callback
    license_mgr = LicenseManager(on_register_again=lambda: app.on_register_again())
    app.license_mgr = license_mgr
    # Enforce license validation
    if not license_mgr.enforce_license(app.parent):
        logging.error("License validation failed. Exiting application.")
        app.parent.destroy()
        return
    # Check for license expiry reminder after successful license enforcement
    license_mgr.check_expiry_reminder(app.parent)
    root.deiconify()  # Show the main window after splash

def run_application():
    setup_logging()
    if getattr(sys, 'frozen', False):
        os.chdir(os.path.dirname(sys.executable))
    root = tk.Tk()
    root.withdraw()
    splash, progress = show_splash(root, "Starting SANI BROADBAND...")
    root.after(2000, lambda: start_main_app(root, splash, progress))
    root.mainloop()

if __name__ == "__main__":
    run_application()
