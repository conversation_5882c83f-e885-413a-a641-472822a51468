#!/usr/bin/env python3
"""
Simple test to identify the exact point of failure
"""
import sys
import os
import traceback

# Add project directory to sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_step(step_name, test_func):
    """Test a single step and report results"""
    print(f"Testing: {step_name}...")
    try:
        result = test_func()
        print(f"✅ {step_name}: SUCCESS")
        return True
    except Exception as e:
        print(f"❌ {step_name}: FAILED - {e}")
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def test_basic_imports():
    """Test basic Python imports"""
    import tkinter as tk
    import sqlite3
    import os
    return True

def test_pil_import():
    """Test PIL import"""
    from PIL import Image, ImageTk
    return True

def test_database_import():
    """Test database module import"""
    from database import Database
    return True

def test_license_manager_import():
    """Test license manager import"""
    from license_manager import LicenseManager
    return True

def test_login_import():
    """Test login module import"""
    from login import LoginPage
    return True

def test_main_import():
    """Test main module import"""
    from main import App
    return True

def test_tkinter_window():
    """Test basic Tkinter window creation"""
    import tkinter as tk
    root = tk.Tk()
    root.withdraw()
    root.title("Test")
    root.destroy()
    return True

def test_database_creation():
    """Test database object creation"""
    from database import Database
    db = Database()
    return True

def test_license_manager_creation():
    """Test license manager object creation"""
    from license_manager import LicenseManager
    lm = LicenseManager()
    return True

def main():
    """Run all tests in sequence"""
    print("🔍 Running simple diagnostic tests...")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("PIL Import", test_pil_import),
        ("Tkinter Window", test_tkinter_window),
        ("Database Import", test_database_import),
        ("License Manager Import", test_license_manager_import),
        ("Login Import", test_login_import),
        ("Main Import", test_main_import),
        ("Database Creation", test_database_creation),
        ("License Manager Creation", test_license_manager_creation),
    ]
    
    failed_tests = []
    
    for test_name, test_func in tests:
        if not test_step(test_name, test_func):
            failed_tests.append(test_name)
            print(f"\n🛑 Stopping at first failure: {test_name}")
            break
    
    print("\n" + "=" * 50)
    if failed_tests:
        print(f"❌ FAILED at: {failed_tests[0]}")
        print("This is likely where your application is crashing.")
    else:
        print("✅ All basic tests passed!")
        print("The issue might be in the application flow or license enforcement.")
    
    print("=" * 50)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Test script crashed: {e}")
        traceback.print_exc()
    
    input("Press Enter to exit...")
