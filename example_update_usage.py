#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Example: How to use the License Update System
This demonstrates the complete workflow for updating license keys
"""

from create_update_package import create_update_package, create_bulk_update_package
from license_manager import LicenseManager

def example_single_key_update():
    """Example: Create a single license key update"""
    print("EXAMPLE 1: Single License Key Update")
    print("=" * 50)
    
    # Create update package for a single customer
    update_file = create_update_package(
        customer_name="John Doe",
        email="<EMAIL>",
        license_type="full",
        days_valid=365,
        activations=1000,
        version="2026.1.0",
        description="2026 license key for <PERSON>"
    )
    
    print(f"\nUpdate file created: {update_file}")
    print("This file can be distributed to the client.")

def example_bulk_key_update():
    """Example: Create bulk license key update"""
    print("\nEXAMPLE 2: Bulk License Key Update")
    print("=" * 50)
    
    # Define multiple customers
    customers = [
        {
            "customer_name": "Company A",
            "email": "<EMAIL>",
            "license_type": "full",
            "days_valid": 365,
            "activations": 1000
        },
        {
            "customer_name": "Company B", 
            "email": "<EMAIL>",
            "license_type": "full",
            "days_valid": 365,
            "activations": 500
        },
        {
            "customer_name": "Company C",
            "email": "<EMAIL>", 
            "license_type": "full",
            "days_valid": 365,
            "activations": 2000
        }
    ]
    
    # Create bulk update package
    update_file = create_bulk_update_package(
        keys_info=customers,
        version="2026.1.0",
        description="2026 license keys for all customers"
    )
    
    print(f"\nBulk update file created: {update_file}")
    print("This file can be distributed to all clients.")

def example_apply_update():
    """Example: How the application applies updates"""
    print("\nEXAMPLE 3: Applying Updates in Application")
    print("=" * 50)
    
    # Simulate application startup
    print("1. Application starting...")
    lm = LicenseManager()
    
    print(f"2. Current default keys: {len(lm.default_keys)}")
    for key, info in lm.default_keys.items():
        print(f"   - {key}: {info['name']}")
    
    print("3. Checking for updates...")
    update_applied = lm.check_for_updates()
    
    if update_applied:
        print("4. ✓ Update applied successfully!")
        print(f"5. Updated default keys: {len(lm.default_keys)}")
        for key, info in lm.default_keys.items():
            print(f"   - {key}: {info['name']}")
    else:
        print("4. ✓ No updates found")

def example_distribution_workflow():
    """Example: Complete distribution workflow"""
    print("\nEXAMPLE 4: Complete Distribution Workflow")
    print("=" * 50)
    
    print("STEP 1: Generate new license keys")
    print("  python create_update_package.py --customer 'Client Name' --email '<EMAIL>' --version '2026.1.0'")
    
    print("\nSTEP 2: Distribute update file to client")
    print("  - Send license_update_2026.1.0.json to client")
    print("  - Client places file in: %APPDATA%\\CRM_System\\")
    print("  - Client renames file to: license_update.json")
    
    print("\nSTEP 3: Client restarts application")
    print("  - Application automatically detects update file")
    print("  - Application applies new license keys")
    print("  - Update file is automatically removed")
    
    print("\nSTEP 4: Client can now use new license keys")
    print("  - New keys are available in the application")
    print("  - Old keys remain valid until expiry")
    print("  - Client tracking prevents key reuse")

if __name__ == "__main__":
    # Run examples
    example_single_key_update()
    example_bulk_key_update()
    example_apply_update()
    example_distribution_workflow()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print("✓ Update system allows adding new license keys without full reinstall")
    print("✓ Clients receive small JSON files instead of full installers")
    print("✓ Application automatically applies updates on startup")
    print("✓ System maintains backward compatibility with existing keys")
    print("✓ Client tracking prevents key reuse and abuse")
    print("=" * 60) 