#!/usr/bin/env python3
"""
Test version of main.py with license validation bypassed
"""
import tkinter as tk
import os
import sys
from PIL import Image, ImageTk
import logging

# Add project directory to sys.path to ensure module resolution
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    path = os.path.join(base_path, relative_path)
    path = os.path.normpath(path)  # Normalize path for different OS
    
    # Debugging - verify the path exists
    if not os.path.exists(path):
        logging.warning(f"Resource path not found: {path}")
    
    return path

def setup_logging():
    """Configure logging for license validation"""
    log_dir = os.path.join(os.getenv('APPDATA', ''), 'CRM_System')
    os.makedirs(log_dir, exist_ok=True)
    log_path = os.path.join(log_dir, 'crm_test.log')
    
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_path),
            logging.StreamHandler()
        ])
    
    return log_path

def show_splash(root):
    splash = tk.Toplevel(root)
    splash.overrideredirect(True)
    splash.geometry("400x350+600+300")
    
    # Use absolute path for the logo
    if getattr(sys, 'frozen', False):
        base_path = sys._MEIPASS
    else:
        base_path = os.path.dirname(os.path.abspath(__file__))
    
    logo_path = os.path.join(base_path, 'assets', 'Logo.png')
    if not os.path.exists(logo_path):
        logo_path = r'D:\CRM_System\assets\Logo.png'
    
    try:
        img = Image.open(logo_path)
        img = img.resize((200, 200))
        photo = ImageTk.PhotoImage(img)
        label = tk.Label(splash, image=photo, bg='#F8F9FA')
        label.image = photo
        label.pack(expand=True, pady=(30, 10))
    except Exception as e:
        logging.warning(f"Could not load logo: {e}")
        # Fallback text
        tk.Label(splash, text="CRM SYSTEM", font=("Helvetica", 24, "bold"), 
                bg='#F8F9FA', fg='#4A6FA5').pack(expand=True, pady=(50, 10))
    
    loading = tk.Label(splash, text="Loading... (Test Mode)", font=("Helvetica", 14), bg='#F8F9FA')
    loading.pack()
    splash.configure(bg='#F8F9FA')
    splash.update_idletasks()
    w = splash.winfo_width()
    h = splash.winfo_height()
    ws = splash.winfo_screenwidth()
    hs = splash.winfo_screenheight()
    x = (ws // 2) - (w // 2)
    y = (hs // 2) - (h // 2)
    splash.geometry(f"400x350+{x}+{y}")
    return splash

def start_main_app(root, splash):
    try:
        splash.destroy()
        logging.info("Splash screen destroyed, starting main application...")
        
        # Import the App class
        from main import App
        
        app = App(root, None)  # Pass None for license manager
        app.pack(fill="both", expand=True)
        logging.info("Main app created successfully")
        
        # SKIP LICENSE VALIDATION ENTIRELY FOR TESTING
        logging.info("SKIPPING license validation for testing...")
        
        # Show the main window
        root.deiconify()
        logging.info("Main window shown successfully")
        
        # Show a test message
        tk.messagebox.showinfo("Test Mode", "Application started successfully in test mode!\nLicense validation was bypassed.")
        
    except Exception as e:
        logging.error(f"Error starting main application: {str(e)}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")
        try:
            splash.destroy()
        except:
            pass
        try:
            root.destroy()
        except:
            pass
        sys.exit(1)

def run_test_application():
    try:
        log_path = setup_logging()
        logging.info("Starting CRM System application in TEST MODE...")
        
        if getattr(sys, 'frozen', False):
            os.chdir(os.path.dirname(sys.executable))
        
        root = tk.Tk()
        root.withdraw()
        root.title("CRM System - Test Mode")
        
        # Set up proper window closing behavior
        def on_closing():
            logging.info("Application closing...")
            root.quit()
            sys.exit(0)
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        logging.info("Creating splash screen...")
        splash = show_splash(root)
        
        # Start main app after 2 seconds
        root.after(2000, lambda: start_main_app(root, splash))
        
        logging.info("Starting main event loop...")
        root.mainloop()
        
    except Exception as e:
        logging.error(f"Critical error in run_test_application: {str(e)}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")
        print(f"Application failed to start: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    run_test_application()
