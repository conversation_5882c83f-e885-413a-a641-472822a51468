#!/usr/bin/env python3
"""
Comprehensive diagnostic script to identify why the application disappears after splash screen
"""
import os
import sys
import logging
import traceback
import tkinter as tk
from datetime import datetime

# Add project directory to sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_detailed_logging():
    """Set up comprehensive logging to capture all errors"""
    log_dir = os.path.join(os.getenv('APPDATA', ''), 'CRM_System')
    os.makedirs(log_dir, exist_ok=True)
    log_path = os.path.join(log_dir, 'app_crash_diagnosis.log')
    
    # Clear previous log
    if os.path.exists(log_path):
        os.remove(log_path)
    
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
        handlers=[
            logging.FileHandler(log_path),
            logging.StreamHandler()
        ]
    )
    
    # Log uncaught exceptions
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        logging.critical("Uncaught exception", exc_info=(exc_type, exc_value, exc_traceback))
        print(f"CRITICAL ERROR: {exc_type.__name__}: {exc_value}")
        traceback.print_exception(exc_type, exc_value, exc_traceback)
    
    sys.excepthook = handle_exception
    return log_path

def test_imports():
    """Test all critical imports"""
    logging.info("=== TESTING IMPORTS ===")
    
    imports_to_test = [
        ("tkinter", "tkinter"),
        ("PIL", "PIL.Image"),
        ("database", "database"),
        ("license_manager", "license_manager"),
        ("login", "login"),
        ("views.dashboard", "views.dashboard"),
        ("views.customers", "views.customers"),
        ("views.billing", "views.billing"),
    ]
    
    failed_imports = []
    
    for name, module in imports_to_test:
        try:
            __import__(module)
            logging.info(f"✅ {name}: Import successful")
        except Exception as e:
            logging.error(f"❌ {name}: Import failed - {str(e)}")
            failed_imports.append((name, str(e)))
    
    return failed_imports

def test_database_initialization():
    """Test database initialization"""
    logging.info("=== TESTING DATABASE INITIALIZATION ===")
    
    try:
        from database import Database
        db = Database()
        logging.info("✅ Database initialization successful")
        
        # Test basic database operations
        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        conn.close()
        
        logging.info(f"✅ Database query successful - {user_count} users found")
        return True
        
    except Exception as e:
        logging.error(f"❌ Database initialization failed: {str(e)}")
        logging.error(f"Traceback: {traceback.format_exc()}")
        return False

def test_license_manager():
    """Test license manager initialization"""
    logging.info("=== TESTING LICENSE MANAGER ===")
    
    try:
        from license_manager import LicenseManager
        license_mgr = LicenseManager()
        logging.info("✅ License manager initialization successful")
        
        # Test license enforcement without GUI
        logging.info("Testing license enforcement logic...")
        # Don't actually enforce, just test the object creation
        return True
        
    except Exception as e:
        logging.error(f"❌ License manager initialization failed: {str(e)}")
        logging.error(f"Traceback: {traceback.format_exc()}")
        return False

def test_gui_creation():
    """Test basic GUI creation"""
    logging.info("=== TESTING GUI CREATION ===")
    
    try:
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Test basic window creation
        root.title("Test Window")
        root.geometry("100x100")
        
        logging.info("✅ Basic Tkinter window creation successful")
        
        # Test if we can create a simple frame
        frame = tk.Frame(root)
        frame.pack()
        
        logging.info("✅ Basic frame creation successful")
        
        root.destroy()
        return True
        
    except Exception as e:
        logging.error(f"❌ GUI creation failed: {str(e)}")
        logging.error(f"Traceback: {traceback.format_exc()}")
        return False

def test_main_app_creation():
    """Test main app creation without running mainloop"""
    logging.info("=== TESTING MAIN APP CREATION ===")
    
    try:
        # Import main components
        from main import App
        from license_manager import LicenseManager
        
        # Create root window
        root = tk.Tk()
        root.withdraw()
        
        # Create license manager
        license_mgr = LicenseManager()
        
        # Create app instance
        app = App(root, license_mgr)
        
        logging.info("✅ Main app creation successful")
        
        root.destroy()
        return True
        
    except Exception as e:
        logging.error(f"❌ Main app creation failed: {str(e)}")
        logging.error(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Run comprehensive diagnostics"""
    print("🔍 Starting comprehensive application crash diagnosis...")
    log_path = setup_detailed_logging()
    
    logging.info("=" * 60)
    logging.info("CRM SYSTEM CRASH DIAGNOSIS")
    logging.info(f"Timestamp: {datetime.now()}")
    logging.info(f"Python Version: {sys.version}")
    logging.info(f"Platform: {sys.platform}")
    logging.info(f"Working Directory: {os.getcwd()}")
    logging.info("=" * 60)
    
    tests = [
        ("Import Tests", test_imports),
        ("Database Initialization", test_database_initialization),
        ("License Manager", test_license_manager),
        ("GUI Creation", test_gui_creation),
        ("Main App Creation", test_main_app_creation),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logging.info(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            if result:
                logging.info(f"✅ {test_name}: PASSED")
            else:
                logging.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logging.error(f"❌ {test_name}: CRASHED - {str(e)}")
            logging.error(f"Traceback: {traceback.format_exc()}")
            results[test_name] = False
    
    # Summary
    logging.info("\n" + "=" * 60)
    logging.info("DIAGNOSIS SUMMARY")
    logging.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logging.info(f"{test_name}: {status}")
    
    logging.info(f"\nOverall: {passed}/{total} tests passed")
    logging.info(f"Log file saved to: {log_path}")
    
    if passed < total:
        logging.error("\n🚨 ISSUES FOUND! Check the failed tests above.")
        logging.error("The application is likely crashing due to one of these failures.")
    else:
        logging.info("\n🎉 All tests passed! The issue might be in the application flow or license enforcement.")
    
    print(f"\n📋 Diagnosis complete. Check log file: {log_path}")
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        input("\nPress Enter to exit...")
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Diagnosis script crashed: {e}")
        traceback.print_exc()
        input("\nPress Enter to exit...")
        sys.exit(1)
