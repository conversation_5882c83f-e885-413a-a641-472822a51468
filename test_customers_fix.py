#!/usr/bin/env python3
"""
Test script to verify customers.py syntax fixes
"""
import os
import sys
import logging

# Add project directory to sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_customers_import():
    """Test if customers.py can be imported without syntax errors"""
    try:
        from views.customers import CustomerManager
        logging.info("✅ CustomerManager imported successfully")
        return True
    except SyntaxError as e:
        logging.error(f"❌ Syntax error in customers.py: {str(e)}")
        return False
    except Exception as e:
        logging.error(f"❌ Import error: {str(e)}")
        return False

def test_database_import():
    """Test if database module can be imported"""
    try:
        from database import Database
        logging.info("✅ Database imported successfully")
        return True
    except Exception as e:
        logging.error(f"❌ Database import error: {str(e)}")
        return False

def test_billing_import():
    """Test if billing module can be imported"""
    try:
        from views.billing import BillingManager
        logging.info("✅ BillingManager imported successfully")
        return True
    except Exception as e:
        logging.error(f"❌ BillingManager import error: {str(e)}")
        return False

def main():
    """Run all tests"""
    logging.info("🚀 Testing customers.py syntax fixes...")
    
    tests = [
        ("Database Module", test_database_import),
        ("Customers Module", test_customers_import),
        ("Billing Module", test_billing_import),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logging.info(f"\n📋 Running test: {test_name}")
        try:
            if test_func():
                passed += 1
                logging.info(f"✅ {test_name}: PASSED")
            else:
                logging.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logging.error(f"❌ {test_name}: FAILED with exception: {str(e)}")
    
    logging.info(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logging.info("🎉 All tests passed! Syntax errors have been fixed.")
        logging.info("✅ The customers page should now load without 'expected except or finally' errors")
        logging.info("✅ Manual bill creation should work without database locking")
        return True
    else:
        logging.error("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
