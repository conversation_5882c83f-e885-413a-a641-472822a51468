import gspread
from oauth2client.service_account import ServiceAccountCredentials
import json
import os
import logging
from typing import Optional, Tuple, Callable
from datetime import datetime, timedelta
import hashlib
from cryptography.fernet import Fernet
import time
import pickle
import platform
import subprocess
import uuid
from tkinter import messagebox, simpledialog
import tkinter as tk
from tkinter import ttk
import asyncio
import threading
import inspect

class LicenseManager:
    def __init__(self, on_register_again: Optional[Callable] = None):
        self.license_key = None
        self.license_valid = False
        self.license_expiry = None
        self.last_login = None
        self.last_reminder = None  # Track the last time the reminder was shown
        self.on_register_again = on_register_again  # FIXED: Corrected typo from 'on_register_ington'
        self.app_data_dir = os.path.join(os.getenv('APPDATA', ''), 'CRM_System')
        self.local_license_path = os.path.join(self.app_data_dir, 'user_license.enc')
        self.license_cache_path = os.path.join(self.app_data_dir, 'license_cache.dat')
        self.attempts_log_path = os.path.join(self.app_data_dir, 'attempts.log')
        self.encryption_key_path = os.path.join(self.app_data_dir, 'key.key')
        self.trial_file = os.path.join(self.app_data_dir, 'trial.key')
        self.license_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'license.key')
        self.trial_history_path = os.path.join(self.app_data_dir, 'trial_history.enc')
        self.registry_key_path = os.path.join(self.app_data_dir, 'registry_backup.enc')
        
        # Configuration
        self.max_attempts = 5
        self.lockout_time = 300  # 5 minutes in seconds
        self.offline_grace_period = 30  # Days allowed for offline use
        self.trial_days = 7  # Trial period
        self.full_version_days = 365  # Full version license duration
        self.reminder_interval = 14400  # 4 hours in seconds
        self.secret_salt = "YourSecretSalt123!"  # MUST match generate_license.py
        
        # Enhanced default keys with client tracking
        self.default_keys = {
            "76C4EDB3C004BA89": {
                "name": "Year 1 License",
                "valid_until": "2025-12-31",  # Set your actual expiry date
                "max_activations": 1000,  # Maximum number of different machines
                "current_activations": 0,
                "description": "First year license for all clients"
            },
            "B5E9F3A2C8D701E6": {
                "name": "Year 2 License", 
                "valid_until": "2026-12-31",  # Set your actual expiry date
                "max_activations": 1000,
                "current_activations": 0,
                "description": "Second year license for all clients"
            }
        }
        
        # Client tracking file
        self.client_tracking_path = os.path.join(self.app_data_dir, 'client_tracking.enc')
        
        # Color scheme
        self.COLORS = {
            'background': '#F8F9FA',
            'card_bg': '#FFFFFF',
            'primary_accent': '#4A6FA5',
            'secondary_accent': '#6C8FC7',
            'text_primary': '#2D3748',
            'text_secondary': '#718096',
            'button_start': '#4A6FA5',
            'button_end': '#3A5A8C',
            'transparent': 'SystemTransparent',
            'warning': '#E53E3E',
            'input_bg': '#EDF2F7',
            'border': '#E2E8F0',
            'spinner': '#4A6FA5'
        }
        
        # Ensure app directory exists
        os.makedirs(self.app_data_dir, exist_ok=True)
        
        # Initialize encryption
        self._init_encryption()
        # Load existing license data
        self.check_local_license()

        # Initialize asyncio event loop for async operations
        self.loop = asyncio.new_event_loop()
        threading.Thread(target=self._run_event_loop, daemon=True).start()

    def _run_event_loop(self):
        """Run asyncio event loop in a separate thread"""
        asyncio.set_event_loop(self.loop)
        self.loop.run_forever()

    def _init_encryption(self):
        """Initialize encryption key for local storage"""
        if not os.path.exists(self.encryption_key_path):
            key = Fernet.generate_key()
            with open(self.encryption_key_path, 'wb') as key_file:
                key_file.write(key)
        
        with open(self.encryption_key_path, 'rb') as key_file:
            self.fernet = Fernet(key_file.read())

    def _encrypt_data(self, data: dict) -> bytes:
        """Encrypt sensitive data"""
        return self.fernet.encrypt(json.dumps(data).encode())

    def _decrypt_data(self, encrypted_data: bytes) -> dict:
        """Decrypt sensitive data"""
        return json.loads(self.fernet.decrypt(encrypted_data).decode())

    def _check_rate_limit(self) -> bool:
        """Check if too many failed attempts have occurred"""
        try:
            if not os.path.exists(self.attempts_log_path):
                return True
            
            with open(self.attempts_log_path, 'r') as f:
                attempts = f.readlines()
                recent_failures = [a for a in attempts if float(a.split(':')[0]) > time.time() - self.lockout_time]
                
                if len(recent_failures) >= self.max_attempts:
                    return False
        except Exception as e:
            logging.error(f"Rate limit check error: {str(e)}")
        
        return True

    def _log_failed_attempt(self):
        """Log a failed license attempt"""
        try:
            with open(self.attempts_log_path, 'a') as f:
                f.write(f"{time.time()}:failed\n")
        except Exception as e:
            logging.error(f"Failed to log attempt: {str(e)}")

    def _clear_attempts_log(self):
        """Clear old failed attempts"""
        try:
            if os.path.exists(self.attempts_log_path):
                os.remove(self.attempts_log_path)
        except Exception as e:
            logging.error(f"Failed to clear attempts log: {str(e)}")

    def get_hardware_fingerprint(self):
        """Generate a unique hardware identifier"""
        try:
            # Windows-specific hardware info
            if platform.system() == 'Windows':
                try:
                    disk_serial = subprocess.check_output(
                        "wmic diskdrive get serialnumber", 
                        shell=True, 
                        stderr=subprocess.DEVNULL
                    ).decode().split('\n')[1].strip()
                except:
                    disk_serial = "unknown"
            else:
                disk_serial = "unknown"

            # Cross-platform system info
            system_info = {
                'machine': platform.machine(),
                'processor': platform.processor(),
                'node': platform.node(),
                'disk_serial': disk_serial,
                'mac': ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                       for elements in range(5, -1, -1)])
            }
            
            return hashlib.sha256(str(system_info).encode()).hexdigest()[:12]
        except Exception as e:
            logging.error(f"Hardware fingerprint error: {str(e)}")
            return "unknown"

    def setup_online_verify(self):
        """Initialize Google Sheets connection"""
        try:
            scope = ['https://spreadsheets.google.com/feeds',
                    'https://www.googleapis.com/auth/drive']
            
            creds_paths = [
                os.path.join(self.app_data_dir, 'google_auth.json'),
                os.path.join(os.path.dirname(__file__), 'google_auth.json'),
                r'D:\CRM_System\google_auth.json'
            ]
            
            creds_path = None
            for path in creds_paths:
                if os.path.exists(path):
                    creds_path = path
                    break
            
            if not creds_path:
                logging.error("Google auth credentials not found")
                return None
            
            creds = ServiceAccountCredentials.from_json_keyfile_name(creds_path, scope)
            return gspread.authorize(creds)
        except Exception as e:
            logging.error(f"Google Sheets setup error: {str(e)}")
            return None

    async def verify_license_online_async(self, license_key: str) -> Tuple[bool, Optional[datetime]]:
        """Asynchronous version of online license verification"""
        try:
            gc = self.setup_online_verify()
            if not gc:
                logging.warning("Could not connect to Google Sheets")
                return (False, None)
            
            sheet = gc.open_by_url(
                "https://docs.google.com/spreadsheets/d/1eezyIIAbTf_AYyAeJ_JKvIgSGeTZdG17ksdTaUclfu4/edit#gid=0"
            ).sheet1
            
            records = sheet.get_all_records()
            
            for record in records:
                if record.get('LicenseKey', '').strip() == license_key.strip():
                    active = record.get('Active', '').strip().upper() == "YES"
                    expiry_str = record.get('ExpiryDate', '')
                    
                    expiry_date = None
                    if expiry_str:
                        try:
                            expiry_date = datetime.strptime(expiry_str, '%Y-%m-%d')
                            if datetime.now() > expiry_date:
                                active = False
                        except ValueError:
                            logging.warning(f"Invalid expiry date format: {expiry_str}")
                    
                    return (active, expiry_date)
            
            logging.warning(f"License key not found: {license_key}")
            return (False, None)
        except Exception as e:
            logging.error(f"Online verification error: {str(e)}")
            return (False, None)

    def verify_license_online(self, license_key: str) -> Tuple[bool, Optional[datetime]]:
        """Run async verification in sync context"""
        try:
            future = asyncio.run_coroutine_threadsafe(
                self.verify_license_online_async(license_key),
                self.loop
            )
            return future.result(timeout=10)  # Timeout after 10 seconds
        except asyncio.TimeoutError:
            logging.error("Online verification timed out")
            return (False, None)
        except Exception as e:
            logging.error(f"Online verification sync error: {str(e)}")
            return (False, None)

    def check_local_license_cache(self) -> Optional[dict]:
        """Check for cached license validation"""
        try:
            if os.path.exists(self.license_cache_path):
                with open(self.license_cache_path, 'rb') as f:
                    cache = pickle.load(f)
                    
                    # Check if cache is still valid
                    if cache.get('valid_until', datetime.min) > datetime.now():
                        return cache
        except Exception as e:
            logging.error(f"Error reading license cache: {str(e)}")
        return None

    def save_license_cache(self, license_key: str, expiry_date: Optional[datetime], valid_days: int = 30):
        """Cache license validation for offline use"""
        try:
            cache_data = {
                'license_key': license_key,
                'valid_until': datetime.now() + timedelta(days=valid_days),
                'expiry_date': expiry_date,
                'last_online_verification': datetime.now(),
                'hwid': self.get_hardware_fingerprint()
            }
            
            with open(self.license_cache_path, 'wb') as f:
                pickle.dump(cache_data, f)
        except Exception as e:
            logging.error(f"Error saving license cache: {str(e)}")

    def validate_license_file(self, license_path):
        """Validate the license file with all new checks"""
        try:
            with open(license_path, 'r') as f:
                license = json.load(f)
            
            # Backward compatibility with old license format
            if isinstance(license, str):
                license_key = license.strip()
                if ':' in license_key:
                    license_key, hwid = license_key.split(':')
                    current_hwid = self.get_hardware_fingerprint()
                    if hwid != current_hwid:
                        return False, "License not valid for this hardware"
                
                if license_key in self.default_keys:
                    return True, "Valid legacy license"
                return False, "Invalid legacy license key"
            
            # New license format validation
            if 'data' not in license or 'key' not in license:
                return False, "Invalid license format"
            
            # Check signature
            signature_data = f"{license['key']}{license['data']['created']}{self.secret_salt}"
            expected_sig = hashlib.md5(signature_data.encode()).hexdigest()
            if license['data'].get('signature') != expected_sig:
                return False, "Invalid license signature"
            
            # Check expiration
            if 'expiry' in license['data']:
                try:
                    expiry_date = datetime.strptime(license['data']['expiry'], "%Y-%m-%d")
                    if datetime.now() > expiry_date:
                        return False, f"License expired on {license['data']['expiry']}"
                except:
                    return False, "Invalid expiration date format"
            
            # Check hardware binding
            if 'hwid' in license['data'] and license['data']['hwid']:
                current_hwid = self.get_hardware_fingerprint()
                if license['data']['hwid'] != current_hwid:
                    return False, "License not valid for this hardware"
            
            # Check activation limits
            activations_used = license['data'].get('activations_used', 0)
            activations_allowed = license['data'].get('activations_allowed', 1)
            if activations_used >= activations_allowed:
                return False, "Maximum activations reached"
            
            # Update activation count
            license['data']['activations_used'] = activations_used + 1
            with open(license_path, 'w') as f:
                json.dump(license, f, indent=2)
            
            return True, "License valid"
        
        except Exception as e:
            return False, f"License validation error: {str(e)}"

    def check_local_license(self) -> Optional[dict]:
        """Check for locally stored license, expiry date, last login, and last reminder"""
        try:
            if os.path.exists(self.local_license_path):
                with open(self.local_license_path, 'rb') as f:
                    encrypted_data = f.read()
                    data = self._decrypt_data(encrypted_data)
                    self.license_key = data.get('license_key')
                    if 'expiry_date' in data:
                        self.license_expiry = datetime.strptime(data['expiry_date'], "%Y-%m-%d")
                    if 'last_login' in data:
                        self.last_login = datetime.strptime(data['last_login'], "%Y-%m-%d %H:%M:%S")
                    if 'last_reminder' in data:
                        self.last_reminder = datetime.strptime(data['last_reminder'], "%Y-%m-%d %H:%M:%S")
                    return data
        except Exception as e:
            logging.error(f"Error reading local license: {str(e)}")
        return None

    def save_license_locally(self, license_key: str, expiry_date: Optional[datetime] = None, 
                           last_login: Optional[datetime] = None, last_reminder: Optional[datetime] = None):
        """Store license key, expiry date, last login, and last reminder locally with encryption"""
        try:
            data = {'license_key': license_key}
            if expiry_date:
                data['expiry_date'] = expiry_date.strftime("%Y-%m-%d")
            if last_login:
                data['last_login'] = last_login.strftime("%Y-%m-%d %H:%M:%S")
            if last_reminder:
                data['last_reminder'] = last_reminder.strftime("%Y-%m-%d %H:%M:%S")
            encrypted_data = self._encrypt_data(data)
            with open(self.local_license_path, 'wb') as f:
                f.write(encrypted_data)
        except Exception as e:
            logging.error(f"Error saving license: {str(e)}")

    def _show_loading_indicator(self, root, callback):
        """Show a rotating loading indicator during async operations"""
        dialog = tk.Toplevel(root)
        dialog.title("Validating License")
        dialog.configure(bg=self.COLORS['background'])
        dialog.transient(root)
        dialog.grab_set()
        
        dialog_width = 200
        dialog_height = 200
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - dialog_width) // 2
        y = (screen_height - dialog_height) // 2
        dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

        frame = tk.Frame(dialog, bg=self.COLORS['card_bg'])
        frame.pack(fill="both", expand=True)

        canvas = tk.Canvas(frame, width=100, height=100, bg=self.COLORS['card_bg'], highlightthickness=0)
        canvas.pack(pady=20)

        tk.Label(
            frame,
            text="Validating license...",
            font=("Helvetica", 12),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg']
        ).pack()

        # Create rotating arc
        angle = 0
        arc = canvas.create_arc(
            20, 20, 80, 80,
            start=0, extent=90,
            outline=self.COLORS['spinner'],
            width=4,
            style="arc"
        )

        def rotate():
            nonlocal angle
            if dialog.winfo_exists():
                angle = (angle + 10) % 360
                canvas.itemconfig(arc, start=angle)
                dialog.after(50, rotate)

        dialog.after(50, rotate)

        def check_callback():
            if dialog.winfo_exists():
                try:
                    if callback():
                        dialog.destroy()
                    else:
                        dialog.after(100, check_callback)
                except Exception as e:
                    logging.error(f"Error in loading callback: {str(e)}")
                    dialog.destroy()

        dialog.after(100, check_callback)
        return dialog

    def validate_license(self, license_key: str, root=None) -> bool:
        """Validate license with loading indicator and optimized checks"""
        if not self._check_rate_limit():
            if root:
                self._show_error_message("Too many failed attempts. Please try again later.", root)
            logging.error("Too many failed attempts. Please try again later.")
            return False
        
        # Trim and normalize the license key
        license_key = license_key.strip().upper()
        
        # Check local and cached licenses first to avoid network delay
        cache = self.check_local_license_cache()
        if cache and cache.get('license_key') == license_key:
            if cache.get('hwid') and cache['hwid'] != self.get_hardware_fingerprint():
                logging.warning("Cached license not valid for this hardware")
                self._log_failed_attempt()
                if root:
                    self._show_error_message("Cached license not valid for this hardware.", root)
                return False
                
            if cache.get('expiry_date') and datetime.now() > cache['expiry_date']:
                logging.warning("Cached license has expired")
                self._log_failed_attempt()
                if root:
                    self._show_error_message("Cached license has expired.", root)
                return False
            self.license_key = license_key
            self.license_expiry = cache.get('expiry_date')
            if root:
                self._show_success_message("Valid license found in cache.", root)
            return True
        
        local_data = self.check_local_license()
        if local_data and local_data.get('license_key') == license_key:
            if self.license_expiry and datetime.now() > self.license_expiry:
                logging.warning("Local license has expired")
                self._log_failed_attempt()
                if root:
                    self._show_error_message("Local license has expired.", root)
                return False
            self.license_key = license_key
            if root:
                self._show_success_message("Valid license found locally.", root)
            return True
        
        # Check if it's a default key (legacy support)
        if license_key in self.default_keys:
            # Check eligibility for this machine
            eligible, message = self.check_default_key_eligibility(license_key)
            if not eligible:
                self._log_failed_attempt()
                if root:
                    self._show_error_message(f"Default key not eligible: {message}", root)
                return False
            
            # Track client usage
            if not self.track_client_usage(license_key):
                self._log_failed_attempt()
                if root:
                    self._show_error_message("Failed to track client usage. Key may have been used before.", root)
                return False
            
            # Set expiry based on key configuration
            key_info = self.default_keys[license_key]
            expiry_date = datetime.strptime(key_info['valid_until'], "%Y-%m-%d")
            
            self.save_license_locally(license_key, expiry_date, datetime.now())
            self._clear_attempts_log()
            self.license_key = license_key
            self.license_expiry = expiry_date
            if root:
                self._show_success_message(f"Valid {key_info['name']}. Valid until {expiry_date.strftime('%Y-%m-%d')}.", root)
            return True
        
        # NEW: Use dynamic license validation for non-default keys
        logging.info(f"Attempting dynamic license validation for key: {license_key}")
        dynamic_valid, dynamic_data, error_message = self.validate_dynamic_license(license_key)
        
        if dynamic_valid and dynamic_data:
            # Update activation count
            self.update_license_activation(license_key)
            
            # Save license locally
            expiry_date = dynamic_data.get('expiry_date')
            if expiry_date and isinstance(expiry_date, str):
                expiry_date = datetime.strptime(expiry_date, "%Y-%m-%d")
            
            self.save_license_locally(license_key, expiry_date, datetime.now())
            self.save_license_cache(license_key, expiry_date, self.offline_grace_period)
            self._clear_attempts_log()
            
            self.license_key = license_key
            self.license_expiry = expiry_date
            
            # Show success message with source information
            source = dynamic_data.get('source', 'unknown')
            customer = dynamic_data.get('customer_name', 'Unknown')
            expiry_str = expiry_date.strftime('%Y-%m-%d') if expiry_date else 'unknown'
            
            if root:
                self._show_success_message(
                    f"Valid license for {customer}. Valid until {expiry_str}. (Validated via {source})", 
                    root
                )
            return True
        
        # If dynamic validation failed, try the old methods as fallback
        logging.info(f"Dynamic validation failed: {error_message}. Trying fallback methods...")
        
        # Show loading indicator for online verification (fallback)
        validation_complete = [False]
        validation_result = [False]
        expiry_date_result = [None]

        def online_validation():
            nonlocal validation_result, expiry_date_result
            online_valid, expiry_date = self.verify_license_online(license_key)
            if online_valid:
                self.save_license_locally(license_key, expiry_date, datetime.now())
                self.save_license_cache(license_key, expiry_date, self.offline_grace_period)
                self._clear_attempts_log()
                self.license_key = license_key
                self.license_expiry = expiry_date
                validation_result[0] = True
                expiry_date_result[0] = expiry_date
            validation_complete[0] = True
            return validation_result[0]

        if root:
            self._show_loading_indicator(root, lambda: validation_complete[0])
        
        # Perform online validation (fallback)
        if online_validation():
            if root:
                self._show_success_message(
                    f"License validated online. Valid until {expiry_date_result[0].strftime('%Y-%m-%d') if expiry_date_result[0] else 'unknown'}.",
                    root
                )
            return True
        
        # Check if this is a valid license file (fallback)
        if os.path.exists(self.license_file_path):
            with open(self.license_file_path, 'r') as f:
                try:
                    license_data = json.load(f)
                    if license_data.get('key') == license_key:
                        valid, message = self.validate_license_file(self.license_file_path)
                        if valid:
                            self.license_key = license_key
                            if 'data' in license_data and 'expiry' in license_data['data']:
                                self.license_expiry = datetime.strptime(license_data['data']['expiry'], "%Y-%m-%d")
                            if root:
                                self._show_success_message(f"Valid license file found. {message}", root)
                            return True
                        else:
                            if root:
                                self._show_error_message(f"License file validation failed: {message}", root)
                except Exception as e:
                    logging.error(f"Error validating license file: {str(e)}")
                    if root:
                        self._show_error_message(f"Error validating license file: {str(e)}", root)
        
        self._log_failed_attempt()
        if root:
            self._show_error_message(f"Invalid license key. {error_message if error_message else ''}", root)
        return False

    def check_trial_period(self) -> bool:
        """Check if trial period is active and valid for this machine"""
        if os.path.exists(self.trial_file):
            try:
                with open(self.trial_file, 'r') as f:
                    trial_data = json.load(f)
                
                # Security checks for trial
                if self.detect_clock_manipulation():
                    logging.error("Clock manipulation detected during trial check")
                    if os.path.exists(self.trial_file):
                        os.remove(self.trial_file)
                    return False
                
                if self.detect_virtual_machine():
                    logging.error("Virtual machine detected during trial check")
                    if os.path.exists(self.trial_file):
                        os.remove(self.trial_file)
                    return False
                
                # Validate machine identity
                current_mac = self.get_mac_address()
                current_hwid = self.get_hardware_fingerprint()
                
                stored_mac = trial_data.get('mac_address')
                stored_hwid = trial_data.get('hardware_id')
                
                # Check if trial is for this machine
                if stored_mac and stored_mac != current_mac:
                    logging.error(f"Trial not valid for this machine. Stored MAC: {stored_mac}, Current MAC: {current_mac}")
                    if os.path.exists(self.trial_file):
                        os.remove(self.trial_file)
                    return False
                
                if stored_hwid and stored_hwid != current_hwid:
                    logging.error(f"Trial not valid for this hardware. Stored HWID: {stored_hwid}, Current HWID: {current_hwid}")
                    if os.path.exists(self.trial_file):
                        os.remove(self.trial_file)
                    return False
                
                # Check trial history (additional security)
                if self.check_trial_history():
                    logging.error("Trial history check failed - trial already used on this machine")
                    if os.path.exists(self.trial_file):
                        os.remove(self.trial_file)
                    return False
                
                install_date = datetime.strptime(trial_data['install_date'], "%Y-%m-%d")
                trial_days = trial_data['trial_days']
                days_used = (datetime.now() - install_date).days
                
                if days_used >= trial_days:
                    logging.error(f"Trial expired {days_used - trial_days} days ago")
                    if os.path.exists(self.trial_file):
                        os.remove(self.trial_file)
                    return False
                
                logging.info(f"Trial period: {trial_days - days_used} days remaining")
                return True
            except Exception as e:
                logging.error(f"Trial check error: {str(e)}")
                if os.path.exists(self.trial_file):
                    os.remove(self.trial_file)
                return False
        return False

    def start_trial(self):
        """Start a new trial period with MAC address and hardware tracking"""
        # Check if trial has already been used on this machine
        if self.check_trial_history():
            logging.error("Trial already used on this machine")
            return False
        
        # Get machine identifiers
        current_mac = self.get_mac_address()
        current_hwid = self.get_hardware_fingerprint()
        
        trial_data = {
            'install_date': datetime.now().strftime("%Y-%m-%d"),
            'trial_days': self.trial_days,
            'first_run': True,
            'mac_address': current_mac,
            'hardware_id': current_hwid,
            'machine_name': platform.node()
        }
        
        with open(self.trial_file, 'w') as f:
            json.dump(trial_data, f)
        
        # Save trial history to prevent reuse
        self.save_trial_history()
        
        logging.info(f"New {self.trial_days}-day trial period started for MAC: {current_mac}, HWID: {current_hwid}")
        return True

    def select_version(self, root=None) -> bool:
        """Show dialog for selecting trial or full version"""
        if root:
            dialog = tk.Toplevel(root)
            dialog.title("Select Version")
            dialog.configure(bg=self.COLORS['background'])
            dialog.transient(root)
            dialog.grab_set()
            
            # Center the dialog
            dialog_width = 400
            dialog_height = 200
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()
            x = (screen_width - dialog_width) // 2
            y = (screen_height - dialog_height) // 2
            dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
            
            frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], padx=20, pady=20)
            frame.pack(fill="both", expand=True)
            
            # Check if trial has already been used
            trial_used = self.check_trial_history()
            
            if trial_used:
                tk.Label(
                    frame,
                    text="Trial version has already been used on this machine.\nPlease select Full Version to continue.",
                    font=("Helvetica", 12),
                    fg=self.COLORS['warning'],
                    bg=self.COLORS['card_bg'],
                    wraplength=350
                ).pack(pady=(0, 20))
            else:
                tk.Label(
                    frame,
                    text="Please select version to use:",
                    font=("Helvetica", 12),
                    fg=self.COLORS['text_primary'],
                    bg=self.COLORS['card_bg']
                ).pack(pady=(0, 20))
            
            version_selected = [None]
            
            def select_trial():
                if not trial_used:
                    version_selected[0] = "trial"
                    dialog.destroy()
            
            def select_full():
                version_selected[0] = "full"
                dialog.destroy()
            
            # Trial button (disabled if already used)
            trial_button = tk.Button(
                frame,
                text=f"Trial Version ({self.trial_days} days)" + (" - Already Used" if trial_used else ""),
                font=("Helvetica", 12, "bold"),
                bg=self.COLORS['primary_accent'] if not trial_used else self.COLORS['text_secondary'],
                fg="#FFFFFF",
                activebackground=self.COLORS['secondary_accent'] if not trial_used else self.COLORS['text_secondary'],
                activeforeground="#FFFFFF",
                width=20,
                command=select_trial,
                state="disabled" if trial_used else "normal"
            )
            trial_button.pack(pady=5)
            
            tk.Button(
                frame,
                text="Full Version",
                font=("Helvetica", 12, "bold"),
                bg=self.COLORS['primary_accent'],
                fg="#FFFFFF",
                activebackground=self.COLORS['secondary_accent'],
                activeforeground="#FFFFFF",
                width=20,
                command=select_full
            ).pack(pady=5)
            
            root.wait_window(dialog)
            return version_selected[0]
        return None

    def check_expiry_reminder(self, root=None) -> bool:
        """Check if a reminder should be shown when license has <10 days remaining, but only every 4 hours"""
        if not root:
            return False

        def check_and_show_reminder():
            # Check if enough time has passed since the last reminder (4 hours = 14400 seconds)
            current_time = datetime.now()
            if self.last_reminder:
                time_since_last_reminder = (current_time - self.last_reminder).total_seconds()
                if time_since_last_reminder < self.reminder_interval:  # Less than 4 hours
                    logging.info(f"Skipping reminder, last shown {(time_since_last_reminder/3600):.2f} hours ago")
                    # Schedule the next check
                    time_to_next_check = self.reminder_interval - time_since_last_reminder
                    root.after(int(time_to_next_check * 1000), check_and_show_reminder)
                    return False

            # Check full version remaining days
            if self.license_expiry:
                days_remaining = (self.license_expiry - datetime.now()).days
                if 0 < days_remaining <= 10:
                    self._show_expiry_reminder_dialog(root, days_remaining, "Full Version")
                    self.last_reminder = datetime.now()
                    self.save_license_locally(self.license_key, self.license_expiry, self.last_login, self.last_reminder)
                    # Schedule the next check in 4 hours
                    root.after(self.reminder_interval * 1000, check_and_show_reminder)
                    return True

            # Check trial remaining days
            if os.path.exists(self.trial_file):
                try:
                    with open(self.trial_file, 'r') as f:
                        trial_data = json.load(f)
                    install_date = datetime.strptime(trial_data['install_date'], "%Y-%m-%d")
                    trial_days = trial_data['trial_days']
                    days_used = (datetime.now() - install_date).days
                    days_remaining = trial_days - days_used
                    if 0 < days_remaining <= 10:
                        self._show_expiry_reminder_dialog(root, days_remaining, "Trial")
                        self.last_reminder = datetime.now()
                        self.save_license_locally(self.license_key, self.license_expiry, self.last_login, self.last_reminder)
                        # Schedule the next check in 4 hours
                        root.after(self.reminder_interval * 1000, check_and_show_reminder)
                        return True
                except Exception as e:
                    logging.error(f"Error checking trial expiry for reminder: {str(e)}")
            
            # If no reminder is needed, schedule the next check in 4 hours
            root.after(self.reminder_interval * 1000, check_and_show_reminder)
            return False

        # Schedule the first check after 1 minute (60000 milliseconds)
        root.after(60000, check_and_show_reminder)
        return True

    def _show_expiry_reminder_dialog(self, root=None, days_remaining: int = 0, version_type: str = "Trial"):
        """Show a styled reminder dialog for license expiry"""
        if not root:
            return

        dialog = tk.Toplevel(root)
        dialog.title(f"{version_type} License Expiry Reminder")
        dialog.configure(bg=self.COLORS['background'])
        dialog.transient(root)
        dialog.grab_set()
        
        # Make dialog resizable and set minimum size
        dialog_width = 450
        dialog_height = 220
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - dialog_width) // 2
        y = (screen_height - dialog_height) // 2
        dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        dialog.minsize(dialog_width, dialog_height)
        
        # Automatically close after 1 minute (60000 milliseconds) if no interaction
        def auto_close():
            if dialog.winfo_exists():
                dialog.destroy()
                logging.info("Expiry reminder dialog auto-closed after 1 minute, user continues working")
        
        dialog.after(60000, auto_close)
        
        # Add close button in title bar
        def on_close():
            dialog.destroy()
            # Do not trigger logout; user should continue working
            logging.info("Expiry reminder dialog closed, user continues working")
        
        dialog.protocol("WM_DELETE_WINDOW", on_close)
        
        # Main frame
        frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], padx=20, pady=20)
        frame.pack(fill="both", expand=True)
        
        # Message label
        message = (f"Your {version_type} license will expire in {days_remaining} day{'s' if days_remaining != 1 else ''}.\n"
                   "Please register again to continue using the application without interruption.")
        
        tk.Label(
            frame,
            text=message,
            font=("Helvetica", 12),
            fg=self.COLORS['text_primary'],
            bg=self.COLORS['card_bg'],
            wraplength=dialog_width-40,
            justify="left"
        ).pack(pady=(0, 20), anchor="w")
        
        # Button frame
        button_frame = tk.Frame(frame, bg=self.COLORS['card_bg'])
        button_frame.pack(fill="x", pady=(10, 0))
        
        action_taken = [None]
        
        def remind_later():
            action_taken[0] = "remind_later"
            dialog.destroy()
            logging.info("User chose to be reminded later, continuing work")
        
        def register_again():
            action_taken[0] = "register_again"
            dialog.destroy()
            try:
                if self.on_register_again:
                    self.on_register_again()  # Navigate to login page
                    logging.info("Navigated to login page for re-registration")
                
                root.after(500, lambda: prompt_for_key())
            except Exception as e:
                logging.error(f"Error during re-registration navigation: {str(e)}")
                self._show_error_message(f"Re-registration Error: {str(e)}", root)
        
        def prompt_for_key():
            try:
                if self._prompt_for_default_key(root):
                    if os.path.exists(self.trial_file):
                        self.start_trial()
                    else:
                        self.license_expiry = datetime.now() + timedelta(days=self.full_version_days)
                        self.save_license_locally(self.license_key, self.license_expiry, datetime.now())
                    
                    self._show_success_message(
                        f"{version_type} license renewed. Valid until "
                        f"{self.license_expiry.strftime('%Y-%m-%d') if self.license_expiry else (datetime.now() + timedelta(days=self.trial_days)).strftime('%Y-%m-%d')}.",
                        root
                    )
                else:
                    logging.warning("License key prompt failed or was cancelled")
                    self._show_error_message("Failed to validate new license key. Please try again.", root)
            except Exception as e:
                logging.error(f"Error during license key prompt: {str(e)}")
                self._show_error_message(f"License Prompt Error: {str(e)}", root)
        
        # Remind Later button (left aligned)
        tk.Button(
            button_frame,
            text="Remind Me Later",
            font=("Helvetica", 11, "bold"),
            bg=self.COLORS['primary_accent'],
            fg="#FFFFFF",
            activebackground=self.COLORS['secondary_accent'],
            activeforeground="#FFFFFF",
            width=15,
            command=remind_later
        ).pack(side="left", padx=5)
        
        # Register button (right aligned)
        tk.Button(
            button_frame,
            text="Register Again",
            font=("Helvetica", 11, "bold"),
            bg=self.COLORS['warning'],
            fg="#FFFFFF",
            activebackground='#C53030',
            activeforeground="#FFFFFF",
            width=15,
            command=register_again
        ).pack(side="right", padx=5)
        
        # Center the buttons by adding an expanding frame
        tk.Frame(button_frame, bg=self.COLORS['card_bg']).pack(side="left", expand=True, fill="x")
        
        root.wait_window(dialog)

    def enforce_license(self, root=None) -> bool:
        """Main license enforcement with loading indicator and security checks"""
        # Check rate limiting first
        if not self._check_rate_limit():
            self._show_error_message("Too many failed attempts. Please try again later.", root)
            self.log_license_usage("license_enforcement", False, {"reason": "rate_limit_exceeded"})
            return False
        
        # Security checks
        if self.detect_clock_manipulation():
            self._show_error_message("System clock manipulation detected. Application will now exit.", root)
            self.log_license_usage("license_enforcement", False, {"reason": "clock_manipulation"})
            return False
        
        if self.detect_virtual_machine():
            self._show_error_message("Virtual machine detected. Application is not supported in virtual environments.", root)
            self.log_license_usage("license_enforcement", False, {"reason": "virtual_machine"})
            return False
        
        if self.check_multiple_installations():
            self._show_error_message("Multiple installations detected. Please use only one installation.", root)
            self.log_license_usage("license_enforcement", False, {"reason": "multiple_installations"})
            return False
        
        if not self.check_code_integrity():
            self._show_error_message("Code integrity check failed. Application may have been tampered with.", root)
            self.log_license_usage("license_enforcement", False, {"reason": "code_integrity_failed"})
            return False
        
        if self.detect_debugging_tools():
            self._show_error_message("Debugging tools detected. Application will now exit.", root)
            self.log_license_usage("license_enforcement", False, {"reason": "debugging_tools"})
            return False
        
        if not self.validate_ntp_time():
            self._show_error_message("System time validation failed. Please check your system clock.", root)
            self.log_license_usage("license_enforcement", False, {"reason": "ntp_validation_failed"})
            return False
        
        if self.detect_usage_anomalies():
            self._show_error_message("Unusual usage pattern detected. Please contact support.", root)
            self.log_license_usage("license_enforcement", False, {"reason": "usage_anomaly"})
            return False
        
        # Check if a valid license exists
        if self._three_step_verification():
            # Check if the license has expired
            if self.license_expiry and datetime.now() > self.license_expiry:
                self._show_expiry_reminder_dialog(root, 0, "Full Version")
                # After dialog, if still expired:
                self._show_error_message("License has expired. Application will now exit.", root)
                return False
            # Update last login time
            self.last_login = datetime.now()
            self.save_license_locally(self.license_key, self.license_expiry, self.last_login)
            # Check for expiry reminder after verifying license
            self.check_expiry_reminder(root)
            # Removed the success message - no need to show "Valid license found." every time
            self.license_valid = True
            self.log_license_usage("license_enforcement", True, {"license_type": "full_version"})
            return True

        # Check if trial file exists and is valid
        trial_active = False
        days_remaining = 0
        if os.path.exists(self.trial_file):
            try:
                with open(self.trial_file, 'r') as f:
                    trial_data = json.load(f)
                install_date = datetime.strptime(trial_data['install_date'], "%Y-%m-%d")
                trial_days = trial_data['trial_days']
                days_used = (datetime.now() - install_date).days
                
                if days_used < trial_days:
                    trial_active = True
                    days_remaining = trial_days - days_used
                else:
                    # Show expiry dialog for trial
                    self._show_expiry_reminder_dialog(root, 0, "Trial")
                    # After the dialog, check if trial is still expired
                    if os.path.exists(self.trial_file):
                        with open(self.trial_file, 'r') as f:
                            trial_data = json.load(f)
                        install_date = datetime.strptime(trial_data['install_date'], "%Y-%m-%d")
                        trial_days = trial_data['trial_days']
                        days_used = (datetime.now() - install_date).days
                        if days_used >= trial_days:
                            self._show_error_message("Trial has expired. Application will now exit.", root)
                            return False
                    # Remove expired trial file
                    os.remove(self.trial_file)
            except Exception as e:
                logging.error(f"Trial file read error: {str(e)}")
                # Remove corrupted trial file
                if os.path.exists(self.trial_file):
                    os.remove(self.trial_file)

        # If trial is active, confirm with user
        if trial_active:
            # Update last login time
            self.last_login = datetime.now()
            self.save_license_locally(self.license_key, None, self.last_login)
            # Check for expiry reminder
            self.check_expiry_reminder(root)
            
            dialog = tk.Toplevel(root)
            dialog.title("Trial Active")
            dialog.configure(bg=self.COLORS['background'])
            dialog.transient(root)
            dialog.grab_set()
            
            dialog_width = 400
            dialog_height = 200
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()
            x = (screen_width - dialog_width) // 2
            y = (screen_height - dialog_height) // 2
            dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
            
            frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], padx=20, pady=20)
            frame.pack(fill="both", expand=True)
            
            tk.Label(
                frame,
                text=f"Trial version active. {days_remaining} day{'s' if days_remaining != 1 else ''} remaining.\nDo you want to continue with the trial?",
                font=("Helvetica", 12),
                fg=self.COLORS['text_primary'],
                bg=self.COLORS['card_bg'],
                wraplength=350
            ).pack(pady=(0, 20))
            
            continue_trial = [True]
            
            def continue_with_trial():
                continue_trial[0] = True
                dialog.destroy()
            
            def choose_new_version():
                continue_trial[0] = False
                dialog.destroy()
            
            tk.Button(
                frame,
                text="Continue Trial",
                font=("Helvetica", 12, "bold"),
                bg=self.COLORS['primary_accent'],
                fg="#FFFFFF",
                activebackground=self.COLORS['secondary_accent'],
                activeforeground="#FFFFFF",
                width=15,
                command=continue_with_trial
            ).pack(side="left", padx=5)
            
            tk.Button(
                frame,
                text="Change Version",
                font=("Helvetica", 12, "bold"),
                bg=self.COLORS['warning'],
                fg="#FFFFFF",
                activebackground='#C53030',
                activeforeground="#FFFFFF",
                width=15,
                command=choose_new_version
            ).pack(side="left", padx=5)
            
            root.wait_window(dialog)
            
            if continue_trial[0]:
                # Removed success message - no need to show trial status every time application starts
                self.license_valid = True
                return True
            else:
                # Remove trial file to allow new selection
                if os.path.exists(self.trial_file):
                    os.remove(self.trial_file)

        # Show version selection dialog
        version = self.select_version(root)
        if not version:
            self._show_error_message("No version selected. Application will now exit.", root)
            return False
            
        if version == "trial":
            # Check if trial has already been used on this machine
            if self.check_trial_history():
                self._show_error_message(
                    "Trial version has already been used on this machine. Please purchase a full license to continue.",
                    root
                )
                self.log_license_usage("trial_selection", False, {"reason": "trial_already_used"})
                return False
            
            # Start trial with MAC address tracking
            if not self.start_trial():
                self._show_error_message(
                    "Failed to start trial. Trial may have already been used on this machine.",
                    root
                )
                self.log_license_usage("trial_start", False, {"reason": "start_failed"})
                return False
            
            self.last_login = datetime.now()
            self.save_license_locally(None, None, self.last_login)
            self._show_success_message(f"Trial version started. {self.trial_days} days remaining.", root)
            self.license_valid = True
            self.log_license_usage("trial_start", True, {"trial_days": self.trial_days})
            return True
        elif version == "full":
            # Prompt for license key
            if not self._prompt_for_default_key(root):
                return False
            self.license_valid = True
            return True
        
        return False

    def _three_step_verification(self) -> bool:
        """Perform three-step verification with optimized order"""
        logging.info("Starting three-step license verification")
        
        # Step 1: Check for cached license first (fastest)
        if self._validate_cached_license():
            logging.info("License validated via cached license")
            return True
        
        # Step 2: Check local encrypted license data (fast)
        if self._validate_local_encrypted_license():
            logging.info("License validated via local encrypted license")
            return True
        
        # Step 3: Validate local license.key file
        if self._validate_license_file():
            logging.info("License validated via local license file")
            return True
        
        # Step 4: Validate against Google Sheet (slowest, requires local key)
        if self._validate_google_sheet():
            logging.info("License validated via Google Sheet")
            return True
        
        logging.warning("Three-step verification failed for all methods")
        return False

    def _validate_google_sheet(self, root=None) -> bool:
        """Validate license against Google Sheet with loading indicator"""
        try:
            # Check if we have a local license key first
            local_data = self.check_local_license()
            if not local_data or not local_data.get('license_key'):
                return False
                
            local_key = local_data.get('license_key')
            
            # Show loading indicator only if root is provided
            validation_complete = [False]
            validation_result = [False]
            expiry_date_result = [None]

            def online_validation():
                nonlocal validation_result, expiry_date_result
                online_valid, expiry_date = self.verify_license_online(local_key)
                if online_valid:
                    self.save_license_locally(local_key, expiry_date, datetime.now())
                    self.save_license_cache(local_key, expiry_date, self.offline_grace_period)
                    self._clear_attempts_log()
                    self.license_key = local_key
                    self.license_expiry = expiry_date
                    validation_result[0] = True
                    expiry_date_result[0] = expiry_date
                validation_complete[0] = True
                return validation_result[0]

            if root:
                self._show_loading_indicator(root, lambda: validation_complete[0])
            
            if online_validation():
                # Removed success message - no need to show validation message during normal startup
                self.license_valid = True
                return True
        except Exception as e:
            logging.error(f"Google Sheet validation error: {str(e)}")
            if root:
                self._show_error_message(f"Google Sheet validation error: {str(e)}", root)
        return False

    def _validate_license_file(self) -> bool:
        """Validate local license.key file"""
        try:
            if not os.path.exists(self.license_file_path):
                logging.info("No license file found at %s", self.license_file_path)
                return False
                
            valid, message = self.validate_license_file(self.license_file_path)
            if valid:
                with open(self.license_file_path, 'r') as f:
                    license_data = json.load(f)
                    self.license_key = license_data.get('key')
                    if 'data' in license_data:
                        expiry_str = license_data['data'].get('expiry')
                        if expiry_str:
                            self.license_expiry = datetime.strptime(expiry_str, "%Y-%m-%d")
                    self.license_valid = True
                    return True
            logging.warning(f"License file validation failed: {message}")
        except Exception as e:
            logging.error(f"License file validation error: {str(e)}")
        return False

    def _validate_cached_license(self) -> bool:
        """Validate cached license"""
        cache = self.check_local_license_cache()
        if cache:
            logging.info(f"Found license cache. License key: {cache.get('license_key')}, Expiry: {cache.get('expiry_date')}")
            # Validate hardware binding
            if cache.get('hwid') == self.get_hardware_fingerprint():
                logging.info("Cache hardware binding validated")
                self.license_key = cache['license_key']
                self.license_expiry = cache.get('expiry_date')
                self.license_valid = True
                return True
            else:
                logging.warning("Cache hardware binding failed")
        else:
            logging.info("No valid license cache found")
        return False

    def _validate_local_encrypted_license(self) -> bool:
        """Validate local encrypted license data"""
        try:
            logging.info(f"Checking local encrypted license. License key: {self.license_key}, Expiry: {self.license_expiry}")
            
            # Use the already-loaded license data from __init__
            if self.license_key:
                # Check if license has expired
                if self.license_expiry and datetime.now() > self.license_expiry:
                    logging.warning("Local encrypted license has expired")
                    return False
                
                # For default keys, always consider valid
                if self.license_key in self.default_keys:
                    # Check if the key is still eligible for this machine
                    eligible, message = self.check_default_key_eligibility(self.license_key)
                    if not eligible:
                        logging.warning(f"Default key no longer eligible: {message}")
                        return False
                    
                    # Check if license has expired based on key configuration
                    key_info = self.default_keys[self.license_key]
                    expiry_date = datetime.strptime(key_info['valid_until'], "%Y-%m-%d")
                    if datetime.now() > expiry_date:
                        logging.warning(f"Default key has expired: {key_info['valid_until']}")
                        return False
                    
                    logging.info(f"License validated as default key: {key_info['name']}")
                    self.license_valid = True
                    self.license_expiry = expiry_date
                    return True
                
                # For other keys, check if we have expiry date and it's not expired
                if self.license_expiry and datetime.now() <= self.license_expiry:
                    logging.info("License validated with valid expiry date")
                    self.license_valid = True
                    return True
                
                # If no expiry date, consider it valid (legacy support)
                if not self.license_expiry:
                    logging.info("License validated without expiry date (legacy support)")
                    self.license_valid = True
                    return True
            else:
                logging.info("No license key found in local encrypted data")
                    
        except Exception as e:
            logging.error(f"Error validating local encrypted license: {str(e)}")
        return False

    def _prompt_for_default_key(self, root=None) -> bool:
        """Prompt user for default key if all verifications fail"""
        if not root:
            logging.error("No root provided for license key prompt")
            return False

        attempts = 0
        while attempts < 3:
            try:
                dialog = tk.Toplevel(root)
                dialog.title("License Required")
                dialog.configure(bg=self.COLORS['background'])
                dialog.transient(root)
                dialog.grab_set()
                
                # Center the dialog
                dialog_width = 400
                dialog_height = 200
                screen_width = root.winfo_screenwidth()
                screen_height = root.winfo_screenheight()
                x = (screen_width - dialog_width) // 2
                y = (screen_height - dialog_height) // 2
                dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
                
                frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], padx=20, pady=20)
                frame.pack(fill="both", expand=True)
                
                tk.Label(
                    frame,
                    text="Please enter your license key for Full Version:",
                    font=("Helvetica", 12),
                    fg=self.COLORS['text_primary'],
                    bg=self.COLORS['card_bg']
                ).pack(pady=(0, 10))
                
                license_entry = ttk.Entry(
                    frame,
                    font=("Helvetica", 12),
                    width=30,
                    style='Modern.TEntry'
                )
                license_entry.pack(fill="x", ipady=8)
                
                # Configure entry style to match login.py
                style = ttk.Style()
                style.configure('Modern.TEntry',
                    foreground=self.COLORS['text_primary'],
                    fieldbackground=self.COLORS['input_bg'],
                    borderwidth=1,
                    relief="flat",
                    padding=10
                )
                style.map('Modern.TEntry',
                    fieldbackground=[('active', self.COLORS['input_bg']), ('!disabled', self.COLORS['input_bg'])],
                    bordercolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])],
                    lightcolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])],
                    darkcolor=[('focus', self.COLORS['primary_accent']), ('!focus', self.COLORS['border'])]
                )
                
                button_frame = tk.Frame(frame, bg=self.COLORS['card_bg'])
                button_frame.pack(pady=20)
                
                license_key = [None]
                
                def submit():
                    license_key[0] = license_entry.get().strip()
                    dialog.destroy()
                
                tk.Button(
                    button_frame,
                    text="Submit",
                    font=("Helvetica", 12, "bold"),
                    bg=self.COLORS['primary_accent'],
                    fg="#FFFFFF",
                    activebackground=self.COLORS['secondary_accent'],
                    activeforeground="#FFFFFF",
                    width=10,
                    command=submit
                ).pack(side="left", padx=5)
                
                tk.Button(
                    button_frame,
                    text="Cancel",
                    font=("Helvetica", 12),
                    bg=self.COLORS['warning'],
                    fg="#FFFFFF",
                    activebackground='#C53030',
                    activeforeground="#FFFFFF",
                    width=10,
                    command=lambda: dialog.destroy()
                ).pack(side="left", padx=5)
                
                root.wait_window(dialog)
                license_key = license_key[0]
            except Exception as e:
                logging.error(f"Error creating license key dialog: {str(e)}")
                self._show_error_message(f"Dialog Error: {str(e)}", root)
                return False
            
            if not license_key:
                self._show_error_message("License key is required for Full Version", root)
                return False
            
            if self.validate_license(license_key, root):
                # On successful validation, set a 9-day expiry
                self.license_key = license_key
                self.last_login = datetime.now()
                if os.path.exists(self.trial_file):
                    self.start_trial()
                else:
                    self.license_expiry = datetime.now() + timedelta(days=self.full_version_days)
                    self.save_license_locally(license_key, self.license_expiry, self.last_login)
                self._show_success_message(
                    f"License accepted. Valid until {self.license_expiry.strftime('%Y-%m-%d') if self.license_expiry else (datetime.now() + timedelta(days=self.trial_days)).strftime('%Y-%m-%d')}.",
                    root
                )
                self._clear_attempts_log()
                return True
            else:
                attempts += 1
                remaining = 3 - attempts
                if remaining > 0:
                    self._show_error_message(f"Invalid license key. {remaining} attempts remaining.", root)
        
        self._show_error_message("Maximum attempts reached. Application will now exit.", root)
        return False

    def _show_error_message(self, message: str, root=None):
        """Show error message to user using the provided root with styled dialog"""
        if root:
            try:
                dialog = tk.Toplevel(root)
                dialog.title("Error")
                dialog.configure(bg=self.COLORS['background'])
                dialog.transient(root)
                dialog.grab_set()
                
                dialog_width = 400
                dialog_height = 150
                screen_width = root.winfo_screenwidth()
                screen_height = root.winfo_screenheight()
                x = (screen_width - dialog_width) // 2
                y = (screen_height - dialog_height) // 2
                dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
                
                frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], padx=20, pady=20)
                frame.pack(fill="both", expand=True)
                
                tk.Label(
                    frame,
                    text=message,
                    font=("Helvetica", 12),
                    fg=self.COLORS['warning'],
                    bg=self.COLORS['card_bg'],
                    wraplength=350
                ).pack(pady=(0, 20))
                
                tk.Button(
                    frame,
                    text="OK",
                    font=("Helvetica", 12, "bold"),
                    bg=self.COLORS['primary_accent'],
                    fg="#FFFFFF",
                    activebackground=self.COLORS['secondary_accent'],
                    activeforeground="#FFFFFF",
                    width=10,
                    command=dialog.destroy
                ).pack()
                
                root.wait_window(dialog)
            except Exception as e:
                logging.error(f"Error displaying error message: {str(e)}")
        else:
            logging.error(message)

    def _show_success_message(self, message: str, root=None):
        """Show success message to user using the provided root with styled dialog"""
        if root:
            try:
                dialog = tk.Toplevel(root)
                dialog.title("Success")
                dialog.configure(bg=self.COLORS['background'])
                dialog.transient(root)
                dialog.grab_set()
                
                dialog_width = 400
                dialog_height = 150
                screen_width = root.winfo_screenwidth()
                screen_height = root.winfo_screenheight()
                x = (screen_width - dialog_width) // 2
                y = (screen_height - dialog_height) // 2
                dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
                
                frame = tk.Frame(dialog, bg=self.COLORS['card_bg'], padx=20, pady=20)
                frame.pack(fill="both", expand=True)
                
                tk.Label(
                    frame,
                    text=message,
                    font=("Helvetica", 12),
                    fg=self.COLORS['text_primary'],
                    bg=self.COLORS['card_bg'],
                    wraplength=350
                ).pack(pady=(0, 20))
                
                tk.Button(
                    frame,
                    text="OK",
                    font=("Helvetica", 12, "bold"),
                    bg=self.COLORS['primary_accent'],
                    fg="#FFFFFF",
                    activebackground=self.COLORS['secondary_accent'],
                    activeforeground="#FFFFFF",
                    width=10,
                    command=dialog.destroy
                ).pack()
                
                root.wait_window(dialog)
            except Exception as e:
                logging.error(f"Error displaying success message: {str(e)}")
        else:
            logging.info(message)

    def revoke_license(self):
        """Revoke current license both locally and online"""
        if not self.license_key:
            return False
        
        try:
            # Revoke online
            gc = self.setup_online_verify()
            if gc:
                sheet = gc.open_by_url(
                    "https://docs.google.com/spreadsheets/d/1eezyIIAbTf_AYyAeJ_JKvIgSGeTZdG17ksdTaUclfu4/edit#gid=0"
                ).sheet1
                
                cell = sheet.find(self.license_key)
                if cell:
                    sheet.update_cell(cell.row, 2, "NO")  # Update Active status
            
            # Remove local license files
            for path in [self.local_license_path, self.license_cache_path, self.trial_file]:
                if os.path.exists(path):
                    os.remove(path)
            
            self.license_key = None
            self.license_valid = False
            self.license_expiry = None
            self.last_login = None
            self.last_reminder = None
            
            # Stop the asyncio event loop
            if self.loop.is_running():
                self.loop.call_soon_threadsafe(self.loop.stop)
                self.loop.run_until_complete(self.loop.shutdown_asyncgens())
                self.loop.close()
                
            return True
        except Exception as e:
            logging.error(f"License revocation error: {str(e)}")
            return False

    def check_trial_history(self) -> bool:
        """Check if this machine has already used a trial period"""
        try:
            if os.path.exists(self.trial_history_path):
                with open(self.trial_history_path, 'rb') as f:
                    encrypted_data = f.read()
                    history_data = self._decrypt_data(encrypted_data)
                    
                    # Check if current MAC address is in trial history
                    current_mac = self.get_mac_address()
                    if current_mac in history_data.get('used_macs', []):
                        logging.warning(f"Trial already used on this machine (MAC: {current_mac})")
                        return True
                    
                    # Check if current hardware fingerprint is in trial history
                    current_hwid = self.get_hardware_fingerprint()
                    if current_hwid in history_data.get('used_hwids', []):
                        logging.warning(f"Trial already used on this hardware (HWID: {current_hwid})")
                        return True
        except Exception as e:
            logging.error(f"Error checking trial history: {str(e)}")
        return False

    def save_trial_history(self):
        """Save trial usage to encrypted history file and online tracking"""
        try:
            current_mac = self.get_mac_address()
            current_hwid = self.get_hardware_fingerprint()
            
            # Load existing history or create new
            if os.path.exists(self.trial_history_path):
                with open(self.trial_history_path, 'rb') as f:
                    encrypted_data = f.read()
                    history_data = self._decrypt_data(encrypted_data)
            else:
                history_data = {
                    'used_macs': [],
                    'used_hwids': [],
                    'trial_usage_count': 0,
                    'first_trial_date': datetime.now().strftime("%Y-%m-%d")
                }
            
            # Add current machine to history
            if current_mac not in history_data['used_macs']:
                history_data['used_macs'].append(current_mac)
            if current_hwid not in history_data['used_hwids']:
                history_data['used_hwids'].append(current_hwid)
            
            history_data['trial_usage_count'] = len(history_data['used_macs'])
            history_data['last_trial_date'] = datetime.now().strftime("%Y-%m-%d")
            
            # Encrypt and save locally
            encrypted_data = self._encrypt_data(history_data)
            with open(self.trial_history_path, 'wb') as f:
                f.write(encrypted_data)
            
            # Also save to online tracking for additional security
            self.save_online_trial_usage(current_mac, current_hwid)
                
            logging.info(f"Trial history saved for MAC: {current_mac}, HWID: {current_hwid}")
        except Exception as e:
            logging.error(f"Error saving trial history: {str(e)}")

    def get_mac_address(self) -> str:
        """Get the primary MAC address of the machine"""
        try:
            # Get all network interfaces
            import uuid
            mac = uuid.getnode()
            return ':'.join(['{:02x}'.format((mac >> elements) & 0xff) 
                           for elements in range(5, -1, -1)])
        except Exception as e:
            logging.error(f"Error getting MAC address: {str(e)}")
            return "unknown"

    def reset_trial_history(self):
        """Reset trial history (for testing purposes only)"""
        try:
            if os.path.exists(self.trial_history_path):
                os.remove(self.trial_history_path)
                logging.info("Trial history reset")
            if os.path.exists(self.trial_file):
                os.remove(self.trial_file)
                logging.info("Trial file removed")
            return True
        except Exception as e:
            logging.error(f"Error resetting trial history: {str(e)}")
            return False

    def check_online_trial_usage(self, mac_address: str) -> bool:
        """Check if trial has been used online (Google Sheets tracking)"""
        try:
            gc = self.setup_online_verify()
            if not gc:
                logging.warning("Could not connect to Google Sheets for trial check")
                return False
            
            # You would need to create a trial tracking sheet
            # For now, return False (no online trial usage found)
            return False
        except Exception as e:
            logging.error(f"Online trial check error: {str(e)}")
            return False

    def save_online_trial_usage(self, mac_address: str, hwid: str):
        """Save trial usage to online tracking (Google Sheets)"""
        try:
            gc = self.setup_online_verify()
            if not gc:
                logging.warning("Could not connect to Google Sheets for trial tracking")
                return False
            
            # You would need to create a trial tracking sheet
            # For now, just log the attempt
            logging.info(f"Would save trial usage to online tracking: MAC={mac_address}, HWID={hwid}")
            return True
        except Exception as e:
            logging.error(f"Online trial tracking error: {str(e)}")
            return False

    def detect_clock_manipulation(self) -> bool:
        """Detect if system clock has been manipulated"""
        try:
            # For development and testing, skip strict clock manipulation detection
            # This can be re-enabled for production by uncommenting the code below
            logging.info("Clock manipulation detection disabled - allowing development time changes")
            return False
            
            # Original clock manipulation detection code (commented out for development):
            # # Check if we have a stored timestamp
            # if os.path.exists(self.registry_key_path):
            #     with open(self.registry_key_path, 'rb') as f:
            #         encrypted_data = f.read()
            #         registry_data = self._decrypt_data(encrypted_data)
            #         
            #         last_check = registry_data.get('last_clock_check')
            #         if last_check:
            #             last_check_time = datetime.strptime(last_check, "%Y-%m-%d %H:%M:%S")
            #             current_time = datetime.now()
            #             
            #             # If current time is significantly before last check, clock was manipulated
            #             time_diff = (last_check_time - current_time).total_seconds()
            #             if time_diff > 3600:  # More than 1 hour difference
            #                 logging.warning(f"Clock manipulation detected: {time_diff} seconds difference")
            #                 return True
            # 
            # # Update clock check timestamp
            # self._update_clock_check()
            # return False
        except Exception as e:
            logging.error(f"Clock manipulation check error: {str(e)}")
            return False

    def _update_clock_check(self):
        """Update the clock check timestamp"""
        try:
            registry_data = {}
            if os.path.exists(self.registry_key_path):
                with open(self.registry_key_path, 'rb') as f:
                    encrypted_data = f.read()
                    registry_data = self._decrypt_data(encrypted_data)
            
            registry_data['last_clock_check'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            encrypted_data = self._encrypt_data(registry_data)
            with open(self.registry_key_path, 'wb') as f:
                f.write(encrypted_data)
        except Exception as e:
            logging.error(f"Error updating clock check: {str(e)}")

    def detect_virtual_machine(self) -> bool:
        """Detect if running in a virtual machine"""
        try:
            # For development and testing, allow virtual machines
            # This can be re-enabled for production by uncommenting the code below
            logging.info("VM detection disabled - allowing virtual machines for development")
            return False
            
            # Original VM detection code (commented out for development):
            # Check for common VM indicators
            # vm_indicators = [
            #     'VMware',
            #     'VirtualBox',
            #     'QEMU',
            #     'Xen',
            #     'Microsoft Virtual',
            #     'Parallels'
            # ]
            # 
            # # Check system manufacturer
            # try:
            #     if platform.system() == 'Windows':
            #         import subprocess
            #         result = subprocess.check_output(
            #             "wmic computersystem get manufacturer", 
            #             shell=True, 
            #             stderr=subprocess.DEVNULL
            #         ).decode().lower()
            #         
            #         for indicator in vm_indicators:
            #             if indicator.lower() in result:
            #                 logging.warning(f"VM detected: {indicator}")
            #                 return True
            # except:
            #     pass
            # 
            # # Check for VM-specific processes
            # try:
            #     import psutil
            #     for proc in psutil.process_iter(['name']):
            #         proc_name = proc.info['name'].lower()
            #         for indicator in vm_indicators:
            #             if indicator.lower() in proc_name:
            #                 logging.warning(f"VM process detected: {proc_name}")
            #                 return True
            # except ImportError:
            #     pass
            # 
            # return False
        except Exception as e:
            logging.error(f"VM detection error: {str(e)}")
            return False

    def check_multiple_installations(self) -> bool:
        """Check for multiple installations of the application"""
        try:
            # For development and testing, allow multiple installations
            # This can be re-enabled for production by uncommenting the code below
            logging.info("Multiple installation check disabled - allowing development scenarios")
            return False
            
            # Original multiple installation check code (commented out for development):
            # # Create a unique installation ID
            # install_id = hashlib.md5(
            #     f"{self.get_mac_address()}{self.get_hardware_fingerprint()}{platform.node()}".encode()
            # ).hexdigest()
            # 
            # # Store installation ID in registry backup
            # registry_data = {}
            # if os.path.exists(self.registry_key_path):
            #     with open(self.registry_key_path, 'rb') as f:
            #         encrypted_data = f.read()
            #         registry_data = self._decrypt_data(encrypted_data)
            # 
            # stored_install_id = registry_data.get('installation_id')
            # 
            # if stored_install_id and stored_install_id != install_id:
            #     logging.warning("Multiple installation detected")
            #     return True
            # 
            # # Update installation ID
            # registry_data['installation_id'] = install_id
            # registry_data['install_date'] = datetime.now().strftime("%Y-%m-%d")
            # 
            # encrypted_data = self._encrypt_data(registry_data)
            # with open(self.registry_key_path, 'wb') as f:
            #     f.write(encrypted_data)
            # 
            # return False
        except Exception as e:
            logging.error(f"Multiple installation check error: {str(e)}")
            return False

    def check_code_integrity(self) -> bool:
        """Check if the license manager code has been tampered with"""
        try:
            # For now, skip the strict code integrity check to allow legitimate updates
            # This can be re-enabled later with a more sophisticated approach
            logging.info("Code integrity check skipped - allowing legitimate code updates")
            return True
            
            # Original strict checking code (commented out for now):
            # Calculate hash of critical methods
            # method_hashes = []
            # for method in [self._three_step_verification, self.check_trial_history, self.validate_license, self.enforce_license]:
            #     source = inspect.getsource(method)
            #     method_hash = hashlib.sha256(source.encode()).hexdigest()
            #     method_hashes.append(method_hash)
            # 
            # # Store expected hashes in registry
            # registry_data = {}
            # if os.path.exists(self.registry_key_path):
            #     with open(self.registry_key_path, 'rb') as f:
            #         encrypted_data = f.read()
            #         registry_data = self._decrypt_data(encrypted_data)
            # 
            # stored_hashes = registry_data.get('code_hashes', [])
            # 
            # if stored_hashes and stored_hashes != method_hashes:
            #     logging.warning("Code integrity check failed - possible tampering detected")
            #     return False
            # 
            # # Update stored hashes
            # registry_data['code_hashes'] = method_hashes
            # encrypted_data = self._encrypt_data(registry_data)
            # with open(self.registry_key_path, 'wb') as f:
            #     f.write(encrypted_data)
            # 
            # return True
        except Exception as e:
            logging.error(f"Code integrity check error: {str(e)}")
            return True  # Return True on error to allow application to continue

    def detect_debugging_tools(self) -> bool:
        """Detect if debugging tools are being used"""
        try:
            # For development and testing, allow debugging tools
            # This can be re-enabled for production by uncommenting the code below
            logging.info("Debugging tools detection disabled - allowing development tools")
            return False
            
            # Original debugging detection code (commented out for development):
            # debug_indicators = [
            #     'ollydbg', 'x64dbg', 'windbg', 'ida', 'ghidra', 'cheatengine',
            #     'processhacker', 'processexplorer', 'wireshark', 'fiddler'
            # ]
            # 
            # # Check for debugger processes
            # try:
            #     import psutil
            #     for proc in psutil.process_iter(['name']):
            #         proc_name = proc.info['name'].lower()
            #         for indicator in debug_indicators:
            #             if indicator in proc_name:
            #                 logging.warning(f"Debugging tool detected: {proc_name}")
            #                 return True
            # except ImportError:
            #     pass
            # 
            # # Check for debugger environment variables
            # debug_env_vars = ['DEBUG', 'PYTHONINSPECT', 'PYTHONDEBUG']
            # for var in debug_env_vars:
            #     if os.getenv(var):
            #         logging.warning(f"Debug environment variable detected: {var}")
            #         return True
            # 
            # return False
        except Exception as e:
            logging.error(f"Debug detection error: {str(e)}")
            return False

    def validate_ntp_time(self) -> bool:
        """Validate system time against NTP servers"""
        try:
            # For development and testing, skip strict NTP validation
            # This can be re-enabled for production by uncommenting the code below
            logging.info("NTP time validation disabled - allowing local time for development")
            return True
            
            # Original NTP validation code (commented out for development):
            # import socket
            # import struct
            # 
            # # NTP servers to check against
            # ntp_servers = [
            #     'time.google.com',
            #     'time.windows.com', 
            #     'pool.ntp.org',
            #     'time.nist.gov'
            # ]
            # 
            # for server in ntp_servers:
            #     try:
            #         # Create UDP socket
            #         sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            #         sock.settimeout(5)
            #         
            #         # Send NTP request
            #         ntp_request = b'\x1b' + 47 * b'\0'
            #         sock.sendto(ntp_request, (server, 123))
            #         
            #         # Receive response
            #         data, addr = sock.recvfrom(1024)
            #         sock.close()
            #         
            #         if len(data) >= 48:
            #             # Extract transmit time from NTP response
            #             transmit_time = struct.unpack('!Q', data[40:48])[0]
            #             ntp_time = (transmit_time / 2**32) - 2208988800  # Convert to Unix timestamp
            #             
            #             # Compare with system time
            #             system_time = time.time()
            #             time_diff = abs(system_time - ntp_time)
            #             
            #             # Allow 5 minutes difference
            #             if time_diff > 300:
            #                 logging.warning(f"System time differs from NTP by {time_diff} seconds")
            #                 return False
            #             
            #             logging.info(f"NTP time validation successful with {server}")
            #             return True
            #             
            #     except Exception as e:
            #         logging.debug(f"NTP check failed for {server}: {str(e)}")
            #         continue
            # 
            # logging.warning("All NTP servers failed, falling back to local time validation")
            # return True  # Fallback to local validation
            
        except Exception as e:
            logging.error(f"NTP validation error: {str(e)}")
            return True  # Return True on error to allow application to continue

    def log_license_usage(self, action: str, success: bool, details: dict = None):
        """Log license usage for analytics and monitoring"""
        try:
            usage_data = {
                'timestamp': datetime.now().isoformat(),
                'action': action,
                'success': success,
                'license_key': self.license_key,
                'mac_address': self.get_mac_address(),
                'hardware_id': self.get_hardware_fingerprint(),
                'machine_name': platform.node(),
                'os_info': f"{platform.system()} {platform.release()}",
                'details': details or {}
            }
            
            # Store usage analytics locally
            analytics_file = os.path.join(self.app_data_dir, 'usage_analytics.json')
            analytics = []
            
            if os.path.exists(analytics_file):
                try:
                    with open(analytics_file, 'r') as f:
                        analytics = json.load(f)
                except:
                    analytics = []
            
            # Keep only last 1000 entries
            analytics.append(usage_data)
            if len(analytics) > 1000:
                analytics = analytics[-1000:]
            
            with open(analytics_file, 'w') as f:
                json.dump(analytics, f, indent=2)
            
            # Send to online analytics if available
            self._send_usage_analytics(usage_data)
            
        except Exception as e:
            logging.error(f"Error logging license usage: {str(e)}")

    def _send_usage_analytics(self, usage_data: dict):
        """Send usage analytics to online tracking"""
        try:
            # This would integrate with your analytics service
            # For now, just log the attempt
            logging.info(f"Usage analytics: {usage_data['action']} - {'SUCCESS' if usage_data['success'] else 'FAILED'}")
        except Exception as e:
            logging.error(f"Error sending usage analytics: {str(e)}")

    def detect_usage_anomalies(self) -> bool:
        """Detect unusual usage patterns that might indicate abuse"""
        try:
            # For development and testing, skip usage anomaly detection
            # This can be re-enabled for production by uncommenting the code below
            logging.info("Usage anomaly detection disabled - allowing development usage patterns")
            return False
            
            # Original usage anomaly detection code (commented out for development):
            # analytics_file = os.path.join(self.app_data_dir, 'usage_analytics.json')
            # if not os.path.exists(analytics_file):
            #     return False
            # 
            # with open(analytics_file, 'r') as f:
            #     analytics = json.load(f)
            # 
            # if len(analytics) < 10:
            #     return False
            # 
            # # Check for rapid license validation attempts
            # recent_attempts = [a for a in analytics[-50:] if a['action'] == 'license_validation']
            # if len(recent_attempts) > 20:  # More than 20 attempts in last 50 entries
            #     logging.warning("Detected rapid license validation attempts - possible abuse")
            #     return True
            # 
            # # Check for multiple failed attempts
            # recent_failures = [a for a in analytics[-20:] if not a['success']]
            # if len(recent_failures) > 10:  # More than 10 failures in last 20 entries
            #     logging.warning("Detected multiple failed license attempts - possible abuse")
            #     return True
            # 
            # return False
        except Exception as e:
            logging.error(f"Usage anomaly detection error: {str(e)}")
            return False

    def get_security_status(self) -> dict:
        """Get comprehensive security status report"""
        try:
            status = {
                'timestamp': datetime.now().isoformat(),
                'machine_info': {
                    'mac_address': self.get_mac_address(),
                    'hardware_id': self.get_hardware_fingerprint(),
                    'machine_name': platform.node(),
                    'os_info': f"{platform.system()} {platform.release()}",
                    'platform': platform.platform()
                },
                'license_status': {
                    'license_key': self.license_key,
                    'license_valid': self.license_valid,
                    'license_expiry': self.license_expiry.isoformat() if self.license_expiry else None,
                    'last_login': self.last_login.isoformat() if self.last_login else None
                },
                'trial_status': {
                    'trial_used': self.check_trial_history(),
                    'trial_active': self.check_trial_period(),
                    'trial_file_exists': os.path.exists(self.trial_file)
                },
                'security_checks': {
                    'clock_manipulation': self.detect_clock_manipulation(),
                    'virtual_machine': self.detect_virtual_machine(),
                    'multiple_installations': self.check_multiple_installations(),
                    'code_integrity': self.check_code_integrity(),
                    'debugging_tools': self.detect_debugging_tools(),
                    'ntp_validation': self.validate_ntp_time(),
                    'usage_anomalies': self.detect_usage_anomalies(),
                    'rate_limit_exceeded': not self._check_rate_limit()
                },
                'file_status': {
                    'local_license_exists': os.path.exists(self.local_license_path),
                    'license_cache_exists': os.path.exists(self.license_cache_path),
                    'trial_history_exists': os.path.exists(self.trial_history_path),
                    'registry_backup_exists': os.path.exists(self.registry_key_path),
                    'analytics_exists': os.path.exists(os.path.join(self.app_data_dir, 'usage_analytics.json'))
                }
            }
            
            return status
        except Exception as e:
            logging.error(f"Error generating security status: {str(e)}")
            return {'error': str(e)}

    def export_security_report(self, filepath: str = None) -> bool:
        """Export security status report to file"""
        try:
            if not filepath:
                filepath = os.path.join(self.app_data_dir, f'security_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
            
            status = self.get_security_status()
            
            with open(filepath, 'w') as f:
                json.dump(status, f, indent=2)
            
            logging.info(f"Security report exported to: {filepath}")
            return True
        except Exception as e:
            logging.error(f"Error exporting security report: {str(e)}")
            return False

    # Add new methods for enhanced license validation
    def validate_dynamic_license(self, license_key: str) -> Tuple[bool, Optional[dict], Optional[str]]:
        """
        Validate license keys that are not in the default keys list.
        Supports both online and offline validation with enhanced security.
        """
        try:
            # Trim and normalize
            license_key = license_key.strip().upper()
            
            # Skip if it's a default key (handled separately)
            if license_key in self.default_keys:
                return False, None, "Use default key validation"
            
            # Method 1: Online validation (Google Sheets)
            online_valid, expiry_date = self.verify_license_online(license_key)
            if online_valid:
                return True, {
                    'license_key': license_key,
                    'expiry_date': expiry_date,
                    'source': 'online',
                    'validated_at': datetime.now()
                }, None
            
            # Method 2: Local license file validation
            if os.path.exists(self.license_file_path):
                valid, message = self.validate_license_file(self.license_file_path)
                if valid:
                    with open(self.license_file_path, 'r') as f:
                        license_data = json.load(f)
                        if license_data.get('key') == license_key:
                            expiry = None
                            if 'data' in license_data and 'expiry' in license_data['data']:
                                expiry = datetime.strptime(license_data['data']['expiry'], "%Y-%m-%d")
                            return True, {
                                'license_key': license_key,
                                'expiry_date': expiry,
                                'source': 'local_file',
                                'validated_at': datetime.now()
                            }, None
            
            # Method 3: Offline license validation (for standalone use)
            offline_valid, offline_data = self._validate_offline_license(license_key)
            if offline_valid:
                return True, offline_data, None
            
            return False, None, "License key not found or invalid"
            
        except Exception as e:
            logging.error(f"Dynamic license validation error: {str(e)}")
            return False, None, f"Validation error: {str(e)}"

    def _validate_offline_license(self, license_key: str) -> Tuple[bool, Optional[dict]]:
        """
        Validate license keys offline using cryptographic methods.
        This allows standalone applications to validate licenses without internet.
        """
        try:
            # Check if we have a local license database
            license_db_path = os.path.join(self.app_data_dir, 'license_database.enc')
            if not os.path.exists(license_db_path):
                return False, None
            
            # Load and decrypt license database
            with open(license_db_path, 'rb') as f:
                encrypted_db = f.read()
                license_db = self._decrypt_data(encrypted_db)
            
            # Search for the license key
            for license_entry in license_db.get('licenses', []):
                if license_entry.get('key') == license_key:
                    # Validate the license
                    if self._validate_license_entry(license_entry):
                        return True, {
                            'license_key': license_key,
                            'expiry_date': license_entry.get('expiry_date'),
                            'customer_name': license_entry.get('customer_name'),
                            'email': license_entry.get('email'),
                            'license_type': license_entry.get('license_type'),
                            'source': 'offline_db',
                            'validated_at': datetime.now()
                        }
            
            return False, None
            
        except Exception as e:
            logging.error(f"Offline license validation error: {str(e)}")
            return False, None

    def _validate_license_entry(self, license_entry: dict) -> bool:
        """Validate a single license entry from the database"""
        try:
            # Check if license is active
            if not license_entry.get('active', True):
                return False
            
            # Check expiration
            expiry_str = license_entry.get('expiry_date')
            if expiry_str:
                expiry_date = datetime.strptime(expiry_str, "%Y-%m-%d")
                if datetime.now() > expiry_date:
                    return False
            
            # Check activation limits
            activations_used = license_entry.get('activations_used', 0)
            activations_allowed = license_entry.get('activations_allowed', 1)
            if activations_used >= activations_allowed:
                return False
            
            # Check hardware binding if required
            if license_entry.get('hwid'):
                current_hwid = self.get_hardware_fingerprint()
                if license_entry['hwid'] != current_hwid:
                    return False
            
            return True
            
        except Exception as e:
            logging.error(f"License entry validation error: {str(e)}")
            return False

    def add_license_to_database(self, license_data: dict) -> bool:
        """
        Add a new license to the local encrypted database.
        This allows adding licenses for new users.
        """
        try:
            license_db_path = os.path.join(self.app_data_dir, 'license_database.enc')
            
            # Load existing database or create new one
            if os.path.exists(license_db_path):
                with open(license_db_path, 'rb') as f:
                    encrypted_db = f.read()
                    license_db = self._decrypt_data(encrypted_db)
            else:
                license_db = {
                    'version': 1,
                    'created': datetime.now().isoformat(),
                    'licenses': []
                }
            
            # Add new license
            license_entry = {
                'key': license_data.get('key'),
                'customer_name': license_data.get('customer_name'),
                'email': license_data.get('email'),
                'license_type': license_data.get('license_type', 'full'),
                'created': license_data.get('created'),
                'expiry_date': license_data.get('expiry_date'),
                'activations_allowed': license_data.get('activations_allowed', 1),
                'activations_used': 0,
                'hwid': license_data.get('hwid'),
                'active': True,
                'added_at': datetime.now().isoformat()
            }
            
            # Check if license already exists
            existing_keys = [l.get('key') for l in license_db.get('licenses', [])]
            if license_entry['key'] in existing_keys:
                logging.warning(f"License key {license_entry['key']} already exists in database")
                return False
            
            license_db['licenses'].append(license_entry)
            
            # Encrypt and save
            encrypted_db = self._encrypt_data(license_db)
            with open(license_db_path, 'wb') as f:
                f.write(encrypted_db)
            
            logging.info(f"Added license {license_entry['key']} to database for {license_entry['customer_name']}")
            return True
            
        except Exception as e:
            logging.error(f"Error adding license to database: {str(e)}")
            return False

    def update_license_activation(self, license_key: str) -> bool:
        """Update activation count for a license"""
        try:
            license_db_path = os.path.join(self.app_data_dir, 'license_database.enc')
            if not os.path.exists(license_db_path):
                return False
            
            with open(license_db_path, 'rb') as f:
                encrypted_db = f.read()
                license_db = self._decrypt_data(encrypted_db)
            
            # Find and update the license
            for license_entry in license_db.get('licenses', []):
                if license_entry.get('key') == license_key:
                    current_activations = license_entry.get('activations_used', 0)
                    max_activations = license_entry.get('activations_allowed', 1)
                    
                    if current_activations < max_activations:
                        license_entry['activations_used'] = current_activations + 1
                        license_entry['last_activation'] = datetime.now().isoformat()
                        
                        # Re-encrypt and save
                        encrypted_db = self._encrypt_data(license_db)
                        with open(license_db_path, 'wb') as f:
                            f.write(encrypted_db)
                        
                        logging.info(f"Updated activation count for license {license_key}")
                        return True
                    else:
                        logging.warning(f"License {license_key} has reached activation limit")
                        return False
            
            return False
            
        except Exception as e:
            logging.error(f"Error updating license activation: {str(e)}")
            return False

    def get_license_database_info(self) -> dict:
        """Get information about the license database"""
        try:
            license_db_path = os.path.join(self.app_data_dir, 'license_database.enc')
            if not os.path.exists(license_db_path):
                return {'exists': False, 'license_count': 0}
            
            with open(license_db_path, 'rb') as f:
                encrypted_db = f.read()
                license_db = self._decrypt_data(encrypted_db)
            
            licenses = license_db.get('licenses', [])
            active_licenses = [l for l in licenses if l.get('active', True)]
            
            return {
                'exists': True,
                'version': license_db.get('version'),
                'created': license_db.get('created'),
                'total_licenses': len(licenses),
                'active_licenses': len(active_licenses),
                'license_types': list(set(l.get('license_type', 'unknown') for l in licenses))
            }
            
        except Exception as e:
            logging.error(f"Error getting license database info: {str(e)}")
            return {'exists': False, 'error': str(e)}

    def track_client_usage(self, license_key: str, client_info: dict = None) -> bool:
        """Track client usage of default keys to prevent reuse"""
        try:
            # Load existing tracking data
            tracking_data = {}
            if os.path.exists(self.client_tracking_path):
                with open(self.client_tracking_path, 'rb') as f:
                    encrypted_data = f.read()
                    tracking_data = self._decrypt_data(encrypted_data)
            
            # Get current machine info
            current_mac = self.get_mac_address()
            current_hwid = self.get_hardware_fingerprint()
            machine_name = platform.node()
            
            # Initialize tracking for this license key if not exists
            if license_key not in tracking_data:
                tracking_data[license_key] = {
                    'activations': [],
                    'total_activations': 0,
                    'first_activation': datetime.now().isoformat(),
                    'last_activation': None
                }
            
            # Check if this machine has already used this key
            for activation in tracking_data[license_key]['activations']:
                if (activation.get('mac_address') == current_mac or 
                    activation.get('hardware_id') == current_hwid):
                    logging.warning(f"License key {license_key} already used on this machine")
                    return False
            
            # Add new activation
            activation_record = {
                'mac_address': current_mac,
                'hardware_id': current_hwid,
                'machine_name': machine_name,
                'activation_date': datetime.now().isoformat(),
                'client_info': client_info or {},
                'os_info': f"{platform.system()} {platform.release()}"
            }
            
            tracking_data[license_key]['activations'].append(activation_record)
            tracking_data[license_key]['total_activations'] = len(tracking_data[license_key]['activations'])
            tracking_data[license_key]['last_activation'] = datetime.now().isoformat()
            
            # Check if we've exceeded max activations for this key
            if license_key in self.default_keys:
                max_activations = self.default_keys[license_key].get('max_activations', 1000)
                if tracking_data[license_key]['total_activations'] > max_activations:
                    logging.error(f"License key {license_key} has exceeded maximum activations")
                    return False
            
            # Save tracking data
            encrypted_data = self._encrypt_data(tracking_data)
            with open(self.client_tracking_path, 'wb') as f:
                f.write(encrypted_data)
            
            logging.info(f"Client usage tracked for license {license_key} on machine {machine_name}")
            return True
            
        except Exception as e:
            logging.error(f"Error tracking client usage: {str(e)}")
            return False

    def check_default_key_eligibility(self, license_key: str) -> Tuple[bool, str]:
        """Check if a default key can be used on this machine"""
        try:
            if license_key not in self.default_keys:
                return False, "Not a valid default key"
            
            # Check if key has expired
            key_info = self.default_keys[license_key]
            valid_until = datetime.strptime(key_info['valid_until'], "%Y-%m-%d")
            if datetime.now() > valid_until:
                return False, f"License expired on {key_info['valid_until']}"
            
            # Check if this machine has already used this key
            if os.path.exists(self.client_tracking_path):
                with open(self.client_tracking_path, 'rb') as f:
                    encrypted_data = f.read()
                    tracking_data = self._decrypt_data(encrypted_data)
                
                if license_key in tracking_data:
                    current_mac = self.get_mac_address()
                    current_hwid = self.get_hardware_fingerprint()
                    
                    for activation in tracking_data[license_key]['activations']:
                        if (activation.get('mac_address') == current_mac or 
                            activation.get('hardware_id') == current_hwid):
                            return False, "This license key has already been used on this machine"
            
            # Check activation limits
            if os.path.exists(self.client_tracking_path):
                with open(self.client_tracking_path, 'rb') as f:
                    encrypted_data = f.read()
                    tracking_data = self._decrypt_data(encrypted_data)
                
                if license_key in tracking_data:
                    current_activations = tracking_data[license_key]['total_activations']
                    max_activations = key_info.get('max_activations', 1000)
                    
                    if current_activations >= max_activations:
                        return False, f"License key has reached maximum activations ({max_activations})"
            
            return True, "Key is eligible for use"
            
        except Exception as e:
            logging.error(f"Error checking default key eligibility: {str(e)}")
            return False, f"Error: {str(e)}"

    def get_client_tracking_report(self) -> dict:
        """Get a report of all client usage for default keys"""
        try:
            if not os.path.exists(self.client_tracking_path):
                return {'error': 'No tracking data found'}
            
            with open(self.client_tracking_path, 'rb') as f:
                encrypted_data = f.read()
                tracking_data = self._decrypt_data(encrypted_data)
            
            report = {
                'generated_at': datetime.now().isoformat(),
                'total_keys_tracked': len(tracking_data),
                'keys': {}
            }
            
            for license_key, key_data in tracking_data.items():
                key_info = self.default_keys.get(license_key, {})
                report['keys'][license_key] = {
                    'name': key_info.get('name', 'Unknown'),
                    'valid_until': key_info.get('valid_until', 'Unknown'),
                    'total_activations': key_data['total_activations'],
                    'max_activations': key_info.get('max_activations', 1000),
                    'first_activation': key_data.get('first_activation'),
                    'last_activation': key_data.get('last_activation'),
                    'activations': key_data['activations']
                }
            
            return report
            
        except Exception as e:
            logging.error(f"Error generating client tracking report: {str(e)}")
            return {'error': str(e)}

    def export_client_report(self, filepath: str = None) -> bool:
        """Export client tracking report to file"""
        try:
            if not filepath:
                filepath = os.path.join(self.app_data_dir, f'client_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
            
            report = self.get_client_tracking_report()
            
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2)
            
            logging.info(f"Client report exported to: {filepath}")
            return True
        except Exception as e:
            logging.error(f"Error exporting client report: {str(e)}")
            return False

    def check_for_updates(self):
        """Check for license key updates on startup"""
        try:
            # Check for update files in app data directory
            update_files = [
                os.path.join(self.app_data_dir, 'license_update.json'),
                os.path.join(self.app_data_dir, 'keys_update.json'),
                os.path.join(self.app_data_dir, 'update.json')
            ]
            
            for update_file in update_files:
                if os.path.exists(update_file):
                    with open(update_file, 'r') as f:
                        update_data = json.load(f)
                    
                    if self._apply_key_update(update_data):
                        # Remove the update file after successful application
                        os.remove(update_file)
                        logging.info(f"License key update applied: {update_data.get('version', 'Unknown')}")
                        return True
            
            return False
            
        except Exception as e:
            logging.error(f"Error checking for updates: {str(e)}")
            return False

    def _apply_key_update(self, update_data):
        """Apply license key updates from update file"""
        try:
            new_keys = update_data.get('new_keys', [])
            if not new_keys:
                return False
            
            # Add new keys to default_keys
            keys_added = []
            for key_info in new_keys:
                key = key_info['key']
                self.default_keys[key] = {
                    'name': key_info['name'],
                    'valid_until': key_info['valid_until'],
                    'max_activations': key_info.get('max_activations', 1000),
                    'current_activations': 0,
                    'description': key_info.get('description', '')
                }
                keys_added.append(key)
            
            logging.info(f"Added {len(keys_added)} new license keys: {keys_added}")
            return True
            
        except Exception as e:
            logging.error(f"Error applying key update: {str(e)}")
            return False
