#!/usr/bin/env python3
"""
Test the application without license validation to isolate the issue
"""
import tkinter as tk
from tkinter import messagebox
import os
import sys
import logging
from PIL import Image, ImageTk

# Add project directory to sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """Configure logging"""
    log_dir = os.path.join(os.getenv('APPDATA', ''), 'CRM_System')
    os.makedirs(log_dir, exist_ok=True)
    log_path = os.path.join(log_dir, 'test_app.log')
    
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_path),
            logging.StreamHandler()
        ]
    )
    return log_path

def resource_path(relative_path):
    """Get absolute path to resource"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def show_splash(root):
    """Show splash screen"""
    logging.info("Creating splash screen...")
    splash = tk.Toplevel(root)
    splash.overrideredirect(True)
    splash.geometry("400x350")
    splash.configure(bg='#F8F9FA')
    
    # Center splash
    splash.update_idletasks()
    w = splash.winfo_width()
    h = splash.winfo_height()
    ws = splash.winfo_screenwidth()
    hs = splash.winfo_screenheight()
    x = (ws // 2) - (w // 2)
    y = (hs // 2) - (h // 2)
    splash.geometry(f"400x350+{x}+{y}")
    
    # Try to load logo
    try:
        logo_path = os.path.join('assets', 'Logo.png')
        if os.path.exists(logo_path):
            img = Image.open(logo_path)
            img = img.resize((200, 200))
            photo = ImageTk.PhotoImage(img)
            label = tk.Label(splash, image=photo, bg='#F8F9FA')
            label.image = photo
            label.pack(expand=True, pady=(30, 10))
        else:
            # Fallback text
            tk.Label(splash, text="CRM SYSTEM", font=("Helvetica", 24, "bold"), 
                    bg='#F8F9FA', fg='#4A6FA5').pack(expand=True, pady=(50, 10))
    except Exception as e:
        logging.warning(f"Could not load logo: {e}")
        tk.Label(splash, text="CRM SYSTEM", font=("Helvetica", 24, "bold"), 
                bg='#F8F9FA', fg='#4A6FA5').pack(expand=True, pady=(50, 10))
    
    loading = tk.Label(splash, text="Loading...", font=("Helvetica", 14), bg='#F8F9FA')
    loading.pack()
    
    logging.info("Splash screen created successfully")
    return splash

def test_database():
    """Test database initialization"""
    logging.info("Testing database initialization...")
    try:
        from database import Database
        db = Database()
        logging.info("✅ Database initialized successfully")
        return True
    except Exception as e:
        logging.error(f"❌ Database initialization failed: {e}")
        return False

def test_login_page():
    """Test login page creation"""
    logging.info("Testing login page creation...")
    try:
        from login import LoginPage
        
        # Create a test window
        test_root = tk.Tk()
        test_root.withdraw()
        
        # Mock navigation commands
        nav_commands = {
            'show_dashboard': lambda: None,
            'show_register': lambda: None,
            'show_forgot_password': lambda: None,
        }
        
        # Create login page
        login_page = LoginPage(test_root, nav_commands)
        logging.info("✅ Login page created successfully")
        
        test_root.destroy()
        return True
    except Exception as e:
        logging.error(f"❌ Login page creation failed: {e}")
        return False

def start_test_app(root, splash):
    """Start the test application without license validation"""
    logging.info("Starting test application...")
    
    try:
        # Destroy splash
        splash.destroy()
        logging.info("Splash screen destroyed")
        
        # Test database
        if not test_database():
            messagebox.showerror("Error", "Database initialization failed")
            root.destroy()
            return
        
        # Test login page
        if not test_login_page():
            messagebox.showerror("Error", "Login page creation failed")
            root.destroy()
            return
        
        # Import and create the main app WITHOUT license manager
        from main import App
        
        logging.info("Creating main app...")
        app = App(root, None)  # Pass None for license manager
        app.pack(fill="both", expand=True)
        
        # Skip license enforcement entirely
        logging.info("Skipping license validation for testing...")
        
        # Show the main window
        root.deiconify()
        logging.info("✅ Main window shown successfully")
        
        messagebox.showinfo("Success", "Application started successfully without license validation!")
        
    except Exception as e:
        logging.error(f"❌ Application startup failed: {e}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")
        messagebox.showerror("Error", f"Application startup failed: {e}")
        root.destroy()

def run_test():
    """Run the test application"""
    log_path = setup_logging()
    logging.info("=" * 50)
    logging.info("TESTING APPLICATION WITHOUT LICENSE VALIDATION")
    logging.info("=" * 50)
    
    try:
        # Create root window
        root = tk.Tk()
        root.withdraw()
        root.title("CRM System - Test Mode")
        root.geometry("1200x800")
        
        # Show splash
        splash = show_splash(root)
        
        # Start app after 2 seconds
        root.after(2000, lambda: start_test_app(root, splash))
        
        # Start main loop
        logging.info("Starting main event loop...")
        root.mainloop()
        
    except Exception as e:
        logging.error(f"❌ Test application crashed: {e}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")
        print(f"Test failed: {e}")
    
    logging.info(f"Log saved to: {log_path}")

if __name__ == "__main__":
    run_test()
